import React from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomTable from '../../../../Components/CustomTable/CustomTable';
import { usePageTitle } from '../../../../Hooks/usePageTitle';
import { inwardPaymentCancellationHeaders } from '../../../../Utils/Constants/TableHeaders';
import { MOCK_INWARD_PAYMENT_DATA } from '../../../../Mocks/MockData';
import withFilters from '../../../../HOC/withFilters ';
import TableActionDropDown from '../../../../Components/TableActionDropDown/TableActionDropDown';
import { HiOutlineTrash } from 'react-icons/hi2';
import withModal from '../../../../HOC/withModal';

const InwardPaymentCancellation = ({ filters, setFilters, showModal, closeModal }) => {
  usePageTitle('Inward Payment Cancellation');
  const tableData = MOCK_INWARD_PAYMENT_DATA;
  const isLoading = false;
  const isError = false;
  const paymentTypeOptions = [
    { label: 'DPV - Payment Voucher', value: 'DPV' },
  ];
  const handleDelete = (id) => {
    showModal('Delete','Are you sure you want to delete this inward payment?',
      () => {
        console.log(id);
        // Close the first modal before showing the second one
        closeModal();
        // Show success modal after a small delay to ensure smooth transition
        setTimeout(() => {
          showModal(
            'Deleted',
            `Inward Payment Deleted successfully`,
            false,
            'success'
          );
        }, 100);
      })
  };

  return (
    <section>
      <h2 className="screen-title mb-3">Inward Payment Cancellation</h2>
      <Row>
        <Col xs={12}>
          <CustomTable
            hideItemsPerPage
            headers={inwardPaymentCancellationHeaders}
            data={tableData}
            filters={filters}
            setFilters={setFilters}
            hideSearch
            dateFilters={[{ title: 'Period from & To' }]}
            selectOptions={[
              {
                title: 'Type',
                options: paymentTypeOptions,
              },
            ]}
            isLoading={isLoading}
            isPaginated={false}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={inwardPaymentCancellationHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.debiteNoteNumber}</td>
                    <td>{item.settlementNo}</td>
                    <td>{item.payDate}</td>
                    <td>{item.account}</td>
                    <td>{item.beneficiary}</td>
                    <td>{item.mode}</td>
                    <td>{item.currency}</td>
                    <td>{item.fcAmount}</td>
                    <td>{item.paidBy}</td>
                    <td>
                      <TableActionDropDown
                        actions={[
                          {
                            name: 'Delete',
                            icon: HiOutlineTrash,
                            onClick: () => handleDelete(item.id),
                            className: 'delete',
                          },
                        ]}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </section>
  );
};
export default withModal(withFilters(InwardPaymentCancellation));
