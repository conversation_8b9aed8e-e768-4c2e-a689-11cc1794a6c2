import axiosInstance from '../../Config/axiosConfig';

// GET Purposes
export const getPurposes = async () => {
  try {
    const { data } = await axiosInstance.get(
      `/user-api/tmn-currency-deal/purpose`
    );
    return data.detail; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};

// GET Banks
export const getBanks = async () => {
  try {
    const { data } = await axiosInstance.get(
      `/user-api/tmn-currency-deal/banks`
    );
    return data.detail; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};