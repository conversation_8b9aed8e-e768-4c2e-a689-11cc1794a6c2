import { ErrorMessage, Form, Formik } from 'formik';
import React from 'react';
import * as Yup from 'yup';
import CustomButton from '../CustomButton';
import CustomInput from '../CustomInput';
import SearchableSelect from '../SearchableSelect/SearchableSelect';

const AddAllocationDetailsForm = ({
  balanceAmount = 0,
  bankOptions = [],
  getAccountsByTypeOptions = [],
  onSuccess,
  onCancel,
  inPopup = false,
}) => {
  const handleSubmit = (values) => {
    if (onSuccess) {
      onSuccess(values);
    }
  };

  const validationSchema = Yup.object().shape({
    ledger: Yup.string().required('Ledger is required'),
    account: Yup.string().required('Account is required'),
    amount: Yup.number()
      .required('Amount is required')
      .positive('Amount must be positive')
      .typeError('Amount must be a number'),
    telex_transfer_amount: Yup.number()
      .required('Telex Transfer Amount is required')
      .positive('Telex Transfer Amount must be positive')
      .typeError('Telex Transfer Amount must be a number'),
    document_type: Yup.string().required('Document Type is required'),
    number: Yup.string().required('Number is required'),
    bank: Yup.string().required('Bank is required'),
    code: Yup.string()
      .required('Code is required')
      .matches(
        /^[a-zA-Z0-9]+$/,
        'Code must only contain alphanumeric characters'
      ),
    city: Yup.string().required('City is required'),
  });

  return (
    <div className={`${inPopup ? 'px-4 pt-2' : 'd-card'}`}>
      <div className="row">
        <div
          className={`${
            inPopup ? 'col-12' : 'col-12 col-lg-10 col-xl-9 col-xxl-7'
          }`}
        >
          <Formik
            initialValues={{
              ledger: '',
              account: '',
              account_name: '',
              telex_transfer_amount: '',
              amount: '',
              balance_amount: balanceAmount,
              document_type: '',
              number: '',
              bank: '',
              bank_name: '',
              code: '',
              city: '1',
              description: '',
            }}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            {({
              values,
              errors,
              touched,
              handleChange,
              handleBlur,
              setFieldValue,
            }) => (
              <Form>
                <div className="row mb-4">
                  <div className="col-12 col-sm-6 mb-45">
                    <SearchableSelect
                      label="Ledger"
                      name="ledger"
                      options={[
                        { label: 'PL', value: 'party' },
                        { label: 'GL', value: 'general' },
                        { label: 'WIC', value: 'walkin' },
                      ]}
                      value={values.ledger}
                      onChange={(e) => {
                        setFieldValue('ledger', e.value);
                      }}
                      placeholder="Select Ledger"
                    />
                    <ErrorMessage
                      name="ledger"
                      component="div"
                      className="input-error-message text-danger"
                    />
                  </div>
                  <div className="col-12 col-sm-6 mb-45">
                    <SearchableSelect
                      label="Account"
                      name="account"
                      options={getAccountsByTypeOptions(values.ledger)}
                      value={values.account}
                      onChange={(e) => {
                        setFieldValue('account', e.value);
                        setFieldValue('account_name', e.label);
                      }}
                      placeholder="Select Account"
                    />
                    <ErrorMessage
                      name="account"
                      component="div"
                      className="input-error-message text-danger"
                    />
                  </div>
                  <div className="col-12 col-sm-6 mb-3">
                    <CustomInput
                      name="telex_transfer_amount"
                      type="number"
                      label="Telex Transfer Amount"
                      placeholder="Enter Telex Transfer Amount"
                      value={values.telex_transfer_amount}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={
                        touched.telex_transfer_amount &&
                        errors.telex_transfer_amount
                      }
                    />
                  </div>
                  <div className="col-12 col-sm-6 mb-3">
                    <CustomInput
                      name="amount"
                      type="number"
                      label="Amount"
                      placeholder="Enter Amount"
                      value={values.amount}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.amount && errors.amount}
                    />
                  </div>
                  <div className="col-12 col-sm-6 mb-3">
                    <CustomInput
                      name="balance_amount"
                      type="number"
                      label="Balance Amount"
                      value={values.balance_amount}
                      readOnly
                    />
                  </div>
                  <div className="col-12 col-sm-6 mb-3">
                    <SearchableSelect
                      label="Document Type"
                      name="document_type"
                      value={values.document_type}
                      options={[
                        {
                          label: 'DDDD',
                          value: 'dddd',
                        },
                        {
                          label: 'DOC',
                          value: 'doc',
                        },
                        {
                          label: 'HAVALE',
                          value: 'havale',
                        },
                        {
                          label: 'Internet',
                          value: 'internet',
                        },
                        {
                          label: 'Ramzdar',
                          value: 'ramzdar',
                        },
                      ]}
                      placeholder="Select Document Type"
                      onChange={(e) => {
                        setFieldValue('document_type', e.value);
                      }}
                    />
                    <ErrorMessage
                      name="document_type"
                      component="div"
                      className="input-error-message text-danger"
                    />
                  </div>
                  <div className="col-12 col-sm-6 mb-3">
                    <CustomInput
                      name="number"
                      type="text"
                      label="Number"
                      placeholder="Enter Number"
                      value={values.number}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.number && errors.number}
                    />
                  </div>
                  <div className="col-12 col-sm-6 mb-3">
                    <SearchableSelect
                      label="Bank"
                      name="bank"
                      value={values.bank}
                      options={bankOptions}
                      placeholder="Select Bank"
                      onChange={(e) => {
                        setFieldValue('bank', e.value);
                        setFieldValue('bank_name', e.label);
                      }}
                      onBlur={handleBlur}
                    />
                    <ErrorMessage
                      name="bank"
                      component="div"
                      className="input-error-message text-danger"
                    />
                  </div>
                  <div className="col-12 col-sm-6 mb-3">
                    {/* Will only accept alpha numeric characters */}
                    <CustomInput
                      name="code"
                      type="text"
                      label="Code"
                      placeholder="Alphanumeric characters only"
                      value={values.code}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.code && errors.code}
                    />
                  </div>
                  <div className="col-12 col-sm-6 mb-3">
                    <SearchableSelect
                      label="City"
                      name="city"
                      options={[]}
                      placeholder="Select City"
                      value={values.city}
                      onChange={(e) => {
                        setFieldValue('city', e.value);
                      }}
                    />
                  </div>
                  <div className="col-12 mb-3">
                    <CustomInput
                      name="description"
                      type="textarea"
                      label="Description"
                      placeholder="Enter Description"
                      value={values.description}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.description && errors.description}
                    />
                  </div>
                </div>
                <div className="d-flex gap-3 ms-auto">
                  <CustomButton type="submit" text="Save" />
                  <CustomButton
                    variant="secondaryButton"
                    text="Cancel"
                    type="button"
                    onClick={onCancel}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </div>
  );
};

export default AddAllocationDetailsForm;
