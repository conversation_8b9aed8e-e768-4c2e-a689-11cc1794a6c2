import React, { useRef, useState, useEffect } from 'react';
import { Formik, Form, ErrorMessage } from 'formik';
import { FaXmark } from 'react-icons/fa6';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomModal from '../../../Components/CustomModal';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import CombinedInputs from '../../../Components/CombinedInputs/CombinedInputs';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  createAccountToAccount,
  getChequeNumbersByBank,
  getAccountBalance,
} from '../../../Services/Transaction/AccountToAccount';
import { showToast } from '../../../Components/Toast/Toast';
import useSettingsStore from '../../../Stores/SettingsStore';
import useFormStore from '../../../Stores/FormStore';
import useAccountsByType from '../../../Hooks/useAccountsByType';
import { useNavigate } from 'react-router-dom';
import { accountToAccountvalidationSchema } from '../../../Utils/Validations/ValidationSchemas';
import { MOCK_EXCHANGE_RATES } from '../../../Mocks/MockData';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';
import { formatFileSize, getIcon } from '../../../Utils/Utils';
import Styles from '../Attachments.module.css';
const NewAccountToAccount = ({
  date,
  isDisabled,
  setIsDisabled,
  currencyOptions,
  setShowAddLedgerModal,
  uploadAttachmentsModal,
  setUploadAttachmentsModal,
  setPageState,
  lastVoucherNumbers,
  setSearchTerm,
  newlyCreatedAccount,
}) => {
  const formikRef = useRef();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  // Form store for Special Commission navigation
  const {
    saveFormValues,
    getFormValues,
    hasFormValues,
    setLastVisitedPage,
    getLastVisitedPage,
    clearLastVisitedPage,
    clearFormValues,
  } = useFormStore();
  const formId = 'account-to-account';

  // Attachments state (following Journal Voucher pattern - starts as array, becomes object)
  const [addedAttachments, setAddedAttachments] = useState([]);
  // Cheque numbers
  const [selectedBank, setSelectedBank] = useState('');
  const [chequeOptions, setChequeOptions] = useState([]);
  // Account selection
  const [selectedDebitAccount, setSelectedDebitAccount] = useState(null);
  const [selectedCreditAccount, setSelectedCreditAccount] = useState(null);
  const [newAccountTriggeredFrom, setNewAccountTriggeredFrom] = useState('');

  // Special Commission state (mirroring Receipt Voucher logic)
  const [specialCommissionValues, setSpecialCommissionValues] = useState({});
  const [addedSpecialCommissionValues, setAddedSpecialCommissionValues] =
    useState(null);
    
    const [showBalances, setShowBalances] = useState(false);
  // Print settings
  const { getPrintSettings, updatePrintSetting } = useSettingsStore();

  // Get account options using custom hook
  const { getAccountsByTypeOptions } = useAccountsByType({
    includeBeneficiary: false,
    staleTime: 1000 * 60 * 5,
  });

  // Restore special commission and form state if returning from special commission page
  useEffect(() => {
    const lastPage = getLastVisitedPage(formId);
    if (
      lastPage === 'special-commission' &&
      hasFormValues(formId) &&
      formikRef.current
    ) {
      const savedValues = getFormValues(formId);
      setSpecialCommissionValues({
        ledger: savedValues?.debitLedger,
        account: savedValues?.debitAccount,
        amount: savedValues?.fcAmount,
        currency: savedValues?.currency,
        date: date,
        current: lastVoucherNumbers?.current,
      });
      formikRef.current.setValues(savedValues);
      setIsDisabled(false);
      if (hasFormValues('special-commission')) {
        setAddedSpecialCommissionValues(getFormValues('special-commission'));
      }
      clearLastVisitedPage(formId);
    }
  }, []);

  useEffect(() => {
    if (lastVoucherNumbers?.current) {
      setSpecialCommissionValues((prev) => ({
        ...prev,
        current: lastVoucherNumbers?.current,
      }));
    }
    if (date) {
      setSpecialCommissionValues((prev) => ({
        ...prev,
        date: date,
      }));
    }
  }, [lastVoucherNumbers?.current, date]);

  // Cheque numbers fetching logic
  useEffect(() => {
    let bankId = null;
    if (
      selectedDebitAccount?.accountType === 'general' &&
      selectedDebitAccount?.value
    ) {
      bankId = selectedDebitAccount.value;
    } else if (
      selectedCreditAccount?.accountType === 'general' &&
      selectedCreditAccount?.bankId
    ) {
      bankId = selectedCreditAccount.bankId;
    }
    if (bankId && bankId !== selectedBank) {
      setSelectedBank(bankId);
    }
  }, [selectedDebitAccount, selectedCreditAccount]);

  const { data: chequeNumbersData, isLoading: isLoadingChequeNumbers } =
    useQuery({
      queryKey: ['chequeNumbers', selectedBank],
      queryFn: () => getChequeNumbersByBank(selectedBank),
      enabled: !!selectedBank,
    });

  useEffect(() => {
    if (chequeNumbersData) {
      setChequeOptions(
        chequeNumbersData.map((cheque) => ({
          label: cheque.cheque_number,
          value: cheque.id,
        }))
      );
    } else if (!selectedBank) {
      setChequeOptions([
        { label: 'Select Bank First', value: null, isDisabled: true },
      ]);
    }
  }, [chequeNumbersData, selectedBank]);

  // Fetch account balances
  const { data: debitAccountBalance } = useQuery({
    queryKey: ['accountBalance', selectedDebitAccount?.value],
    queryFn: () =>
      getAccountBalance(
        selectedDebitAccount.value,
        selectedDebitAccount.accountType
      ),
    enabled: !!selectedDebitAccount?.value,
    staleTime: 1000 * 60 * 2,
    retry: 1,
  });
  const { data: creditAccountBalance } = useQuery({
    queryKey: ['accountBalance', selectedCreditAccount?.value],
    queryFn: () =>
      getAccountBalance(
        selectedCreditAccount.value,
        selectedCreditAccount.accountType
      ),
    enabled: !!selectedCreditAccount?.value,
    staleTime: 1000 * 60 * 2,
  });

  // Submission logic (fix stack error by not calling setState in render or in a loop)
  const createMutation = useMutation({
    mutationFn: createAccountToAccount,
    onSuccess: (data) => {
      showToast('Account to Account Created!', 'success');
      if (getPrintSettings('account_to_account')) {
        if (data?.detail?.pdf_url) {
          window.open(data.detail.pdf_url, '_blank');
        }
      }
      // Invalidate and refetch queries following Receipt Voucher pattern
      queryClient.invalidateQueries(['accountToAccountListing']);
      queryClient.refetchQueries(['accountToAccountListing']);

      // Reset form and state
      handleResetForm();
    },
    onError: (error) => {
      showToast(error.message || 'Error creating Account to Account', 'error');
    },
  });

  const handleSubmit = async () => {
    if (!formikRef.current) return;
    const errors = await formikRef.current.validateForm();
    if (Object.keys(errors).length > 0) {
      formikRef.current.setTouched(
        Object.keys(errors).reduce((acc, key) => {
          acc[key] = true;
          return acc;
        }, {})
      );
      return;
    }
    const values = formikRef.current.values;

    // Map form fields to API fields
    const payload = {
      debit_account_ledger: values.debitLedger,
      debit_account_id: values.debitAccount,
      credit_account_ledger: values.creditLedger,
      credit_account_id: values.creditAccount,
      account_title: values.account_title,
      currency_id: values.currency,
      fc_amount: values.fcAmount,
      debit_account_narration: values.debitNarration,
      credit_account_narration: values.creditNarration,
      comment: values.comment,
      date,
      // Only include cheque_number_id if selected
      ...(values.chequeNumber ? { cheque_number_id: values.chequeNumber } : {}),
      ...addedAttachments,
    };

    // Special Commission (flattened to bracket notation)
    if (addedSpecialCommissionValues) {
      const sc = addedSpecialCommissionValues;
      payload['special_commission[transaction_no]'] = sc.transaction_no;
      payload['special_commission[date]'] = sc.date;
      payload['special_commission[commission_type]'] = sc.commission_type;
      payload['special_commission[ledger]'] = sc.ledger;
      payload['special_commission[account_id]'] = sc.account_id;
      payload['special_commission[currency_id]'] = sc.currency_id;
      payload['special_commission[amount]'] = sc.amount;
      payload['special_commission[description]'] = sc.description;
      payload['special_commission[commission]'] = sc.commission;
      payload['special_commission[total_commission]'] = sc.total_commission;
      if (Array.isArray(sc.distribution)) {
        sc.distribution.forEach((dist, idx) => {
          payload[`special_commission[distribution][${idx}][ledger]`] =
            dist.ledger;
          payload[
            `special_commission[distribution][${idx}][credit_account_id]`
          ] = dist.credit_account_id;
          payload[`special_commission[distribution][${idx}][narration]`] =
            dist.narration;
          payload[`special_commission[distribution][${idx}][percentage]`] =
            dist.percentage;
          payload[`special_commission[distribution][${idx}][amount]`] =
            dist.amount;
        });
      }
    }

    console.log('Final payload with attachments:', payload);
    console.log('addedAttachments:', addedAttachments);
    createMutation.mutate(payload);
  };
  // Fetch exchange rates
  // const { data: exchangeRatesData } = useQuery({
  //   queryKey: ['exchangeRates'],
  //   queryFn: getExchangeRates,
  //   staleTime: 1000 * 60 * 5, // 5 minutes cache
  // });
  // Reset form function following Receipt Voucher pattern
  const handleResetForm = () => {
    setPageState('new');
    setIsDisabled(true);
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
    setAddedAttachments([]);
    clearFormValues(formId);
    clearFormValues('special-commission');
    setAddedSpecialCommissionValues(null);
    // Reset selected accounts
    setSelectedDebitAccount(null);
    setSelectedCreditAccount(null);
    setSelectedBank(null);
    setChequeOptions([]);
  };

  // Handle file removal (following SuspenseVoucher pattern)
  const handleRemoveFile = (file) => {
    setAddedAttachments((prevFiles) => {
      const updatedFiles = { ...prevFiles };

      for (const key in updatedFiles) {
        if (
          updatedFiles[key]?.name === file.name &&
          updatedFiles[key]?.size === file.size
        ) {
          delete updatedFiles[key];
          break;
        }
      }

      return updatedFiles;
    });
  };

  const handleCancel = () => {
    handleResetForm();
  };

  // Special Commission navigation (mirroring Receipt Voucher)
  const handleNavigateToSpecialCommissionPage = () => {
    const requiredFields = [
      'debitLedger',
      'debitAccount',
      'fcAmount',
      'currency',
    ];
    const missingFields = requiredFields.filter(
      (field) => !formikRef.current.values[field]
    );
    if (missingFields.length > 0) {
      const touchedFields = {};
      requiredFields.forEach((field) => {
        touchedFields[field] = true;
      });
      formikRef.current.setTouched({
        ...formikRef.current.touched,
        ...touchedFields,
      });
      return;
    }
    if (formikRef.current && !isDisabled) {
      saveFormValues(formId, formikRef.current.values);
      setLastVisitedPage(formId, 'special-commission');
    }
    const hasExistingCommission = !!addedSpecialCommissionValues;
    setPageState('edit');
    navigate('/transactions/special-commission', {
      state: {
        fromPage: 'account-to-account',
        pageState: 'edit',
        searchTerm: '',
        values: {
          rvValues: {
            ...specialCommissionValues,
            ledger: formikRef.current.values.debitLedger,
            account: formikRef.current.values.debitAccount,
            amount: formikRef.current.values.fcAmount,
            currency: formikRef.current.values.currency,
            date: date,
            current: lastVoucherNumbers?.current,
          },
          isEdit: hasExistingCommission,
        },
      },
    });
  };

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={{
            debitLedger: '',
            debitAccount: '',
            creditLedger: '',
            creditAccount: '',
            chequeNumber: '',
            account_title: 'show',
            currency: '',
            fcAmount: '',
            debitNarration: '',
            creditNarration: '',
            comment: '',
          }}
          validationSchema={accountToAccountvalidationSchema}
          onSubmit={handleSubmit}
        >
          {({
            values,
            errors,
            touched,
            handleChange,
            handleBlur,
            setFieldValue,
          }) => {
            // Helper function to handle account loading
            const handleLedgerChange = (
              ledgerType,
              fieldName,
              setAccountField
            ) => {
              setFieldValue(fieldName, ledgerType);
              setFieldValue(setAccountField, ''); // Clear account when ledger changes
            };

            return (
              <Form>
                <div className="row">
                  <div
                    className={
                      isDisabled
                        ? 'col-12 col-lg-10 col-xl-9 col-xxl-10'
                        : 'col-12 col-lg-10 col-xl-9 col-xxl-9'
                    }
                  >
                    <div className="row">
                      {/* Debit Account Section */}
                      <div className="col-12 col-sm-5 mb-45">
                        <CombinedInputs
                          label="Debit Account"
                          type1="select"
                          type2="select"
                          name1="debitLedger"
                          name2="debitAccount"
                          value1={values.debitLedger}
                          value2={
                            values.debitAccount ||
                            (newlyCreatedAccount?.id &&
                            newAccountTriggeredFrom === 'debit'
                              ? newlyCreatedAccount.id
                              : '')
                          }
                          options1={[
                            { label: 'PL', value: 'party' },
                            { label: 'GL', value: 'general' },
                            { label: 'WIC', value: 'walkin' },
                          ]}
                          options2={getAccountsByTypeOptions(
                            values.debitLedger
                          )}
                          isDisabled={isDisabled}
                          handleBlur={handleBlur}
                          placeholder1="Ledger"
                          placeholder2="Select Account"
                          className1="ledger"
                          className2="account"
                          onChange1={(selected) => {
                            handleLedgerChange(
                              selected.value,
                              'debitLedger',
                              'debitAccount'
                            );
                          }}
                          onChange2={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                              setNewAccountTriggeredFrom('debit');
                            } else {
                              setFieldValue('debitAccount', selected.value);
                              // Track selected debit account for balance fetching
                              setSelectedDebitAccount({
                                value: selected.value,
                                label: selected.label,
                                accountType: values.debitLedger,
                              });
                            }
                          }}
                        />
                      </div>

                      {/* Credit Account Section */}
                      <div className="col-12 col-sm-7 mb-45">
                        <div className="d-flex align-items-end gap-2">
                          <div className="flex-grow-1">
                            <CombinedInputs
                              label="Credit Account"
                              type1="select"
                              type2="select"
                              name1="creditLedger"
                              name2="creditAccount"
                              value1={values.creditLedger}
                              value2={
                                values.creditAccount ||
                                (newlyCreatedAccount?.id &&
                                newAccountTriggeredFrom === 'credit'
                                  ? newlyCreatedAccount.id
                                  : '')
                              }
                              options1={[
                                { label: 'PL', value: 'party' },
                                { label: 'GL', value: 'general' },
                                { label: 'WIC', value: 'walkin' },
                              ]}
                              options2={getAccountsByTypeOptions(
                                values.creditLedger
                              )}
                              isDisabled={isDisabled}
                              handleBlur={handleBlur}
                              placeholder1="Ledger"
                              placeholder2="Select Account"
                              className1="ledger"
                              className2="account"
                              onChange1={(selected) => {
                                handleLedgerChange(
                                  selected.value,
                                  'creditLedger',
                                  'creditAccount'
                                );
                              }}
                              onChange2={(selected) => {
                                if (
                                  selected.label
                                    ?.toLowerCase()
                                    ?.startsWith('add new')
                                ) {
                                  setShowAddLedgerModal(
                                    selected.label?.toLowerCase()
                                  );
                                  setNewAccountTriggeredFrom('credit');
                                } else {
                                  setFieldValue(
                                    'creditAccount',
                                    selected.value
                                  );
                                  // Track selected credit account for balance fetching
                                  setSelectedCreditAccount({
                                    value: selected.value,
                                    label: selected.label,
                                    accountType: values.creditLedger,
                                  });
                                }
                              }}
                            />
                          </div>
                          {/* Switch Account Button */}
                          <div className="d-flex justify-content-end mt-2 flex-shrink-0">
                            <CustomButton
                              text="Switch Account"
                              type="button"
                              variant="secondaryButton"
                              size="sm"
                              disabled={
                                isDisabled ||
                                (!values.debitAccount && !values.creditAccount)
                              }
                              onClick={() => {
                                // Switch debit and credit accounts
                                const tempDebitLedger =
                                  values.debitLedger || '';
                                const tempDebitAccount =
                                  values.debitAccount || '';
                                const tempCreditLedger =
                                  values.creditLedger || '';
                                const tempCreditAccount =
                                  values.creditAccount || '';
                                const tempSelectedDebit = selectedDebitAccount;
                                const tempSelectedCredit =
                                  selectedCreditAccount;

                                // Update form values
                                setFieldValue('debitLedger', tempCreditLedger);
                                setFieldValue(
                                  'debitAccount',
                                  tempCreditAccount
                                );
                                setFieldValue('creditLedger', tempDebitLedger);
                                setFieldValue(
                                  'creditAccount',
                                  tempDebitAccount
                                );

                                // Update selected account states
                                setSelectedDebitAccount(tempSelectedCredit);
                                setSelectedCreditAccount(tempSelectedDebit);

                                console.log('Switched accounts:', {
                                  from: {
                                    debit: tempDebitAccount,
                                    credit: tempCreditAccount,
                                  },
                                  to: {
                                    debit: tempCreditAccount,
                                    credit: tempDebitAccount,
                                  },
                                });
                              }}
                            />
                          </div>
                        </div>
                      </div>

                      {/* Cheque Number */}
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name="chequeNumber"
                          label="Cheque Number"
                          options={chequeOptions}
                          value={values.chequeNumber}
                          onChange={(selected) =>
                            setFieldValue('chequeNumber', selected.value)
                          }
                          onBlur={handleBlur}
                          placeholder="Select Cheque Number"
                          isDisabled={isDisabled}
                        />
                        <ErrorMessage
                          name="chequeNumber"
                          component="div"
                          className="input-error-message text-danger"
                        />
                      </div>

                      {/* Account Title Show/Hide */}
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name="account_title"
                          label="Account Title"
                          options={[
                            { label: 'Show', value: 'show' },
                            { label: 'Hide', value: 'hide' },
                          ]}
                          value={values.account_title}
                          onChange={(selected) =>
                            setFieldValue('account_title', selected.value)
                          }
                          onBlur={handleBlur}
                          placeholder="Show"
                          isDisabled={isDisabled}
                        />
                      </div>

                

                      {/* Currency and FC Amount */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="Currency"
                          type1="select"
                          type2="input"
                          name1="currency"
                          name2="fcAmount"
                          value1={values.currency}
                          value2={values.fcAmount}
                          options1={currencyOptions}
                          isDisabled={isDisabled}
                          handleBlur={handleBlur}
                          placeholder1="Currency"
                          placeholder2="Amount"
                          inputType2="number"
                          className1="currency"
                          className2="amount"
                          onChange1={(selected) => {
                            setFieldValue('currency', selected.value);
                          }}
                          onChange2={handleChange}
                        />
                      </div>
                      <div className="col-0 col-sm-6 mb-45"/>


                      {/* Debit Account Narration */}
                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="debitNarration"
                          label="Debit Account Narration"
                          type="textarea"
                          rows={3}
                          value={values.debitNarration}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter Debit Account Narration"
                          disabled={isDisabled}
                        />
                      </div>

                      {/* Credit Account Narration */}
                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="creditNarration"
                          label="Credit Account Narration"
                          type="textarea"
                          rows={3}
                          value={values.creditNarration}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter Credit Account Narration"
                          disabled={isDisabled}
                        />
                      </div>

                      {/* Comment */}
                      <div className="col-12 mb-45">
                        <CustomInput
                          name="comment"
                          label="Comment"
                          type="textarea"
                          rows={4}
                          value={values.comment}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter Comment"
                          disabled={isDisabled}
                          error={touched.comment && errors.comment}
                        />
                      </div>

                      {/* Attachments Display (following Journal Voucher pattern) */}
                      <div className="col-12 mb-3">
                        <div className="d-flex flex-wrap gap-2">
                          {Object.values(addedAttachments)?.map((file, index) => (
                            <div key={index} style={{ position: 'relative' }}>
                              {console.log('File in NewAccountToAccount:', file)}
                              <div className={Styles.uploadedFiles}>
                                <div className={Styles.nameIconWrapper}>
                                  <div className="beechMein" style={{ minWidth: 28 }}>
                                    {getIcon(file.type)}
                                  </div>
                                  <div
                                    style={{ width: 126 }}
                                    className="d-flex flex-column flex-1"
                                  >
                                    <p className={Styles.fileName}>{file.name}</p>
                                    <p className={Styles.size}>
                                      {formatFileSize(file.size)}
                                    </p>
                                  </div>
                                </div>
                                <button
                                  type="button"
                                  className={Styles.fileRemoveButton}
                                  onClick={() => {
                                    handleRemoveFile(file);
                                  }}
                                  disabled={isDisabled}
                                >
                                  <FaXmark size={16} />
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Add Special Commission Button */}
                    <div className="d-flex mb-5">
                      <CustomButton
                        type="button"
                        onClick={handleNavigateToSpecialCommissionPage}
                        text={`${
                          !!addedSpecialCommissionValues ? 'Edit' : 'Add'
                        } Special Commission`}
                        disabled={isDisabled}
                      />
                    </div>
                    {!!addedSpecialCommissionValues ? (
                      <p
                        style={{
                          color: '#22C55E',
                          fontSize: '14px',
                          marginBottom: '20px',
                        }}
                      >
                        Special Commission has been added successfully.
                      </p>
                    ) : null}

                    {/* Checkboxes */}
                    <div className="d-inline-block mt-3">
                      <CustomCheckbox
                        label="Account Balance"
                        disabled={isDisabled}
                        style={{ border: 'none', margin: 0 }}
                        onChange={(e) => setShowBalances(e.target.checked)}
                      />

                      <CustomCheckbox
                        label="Print"
                        checked={getPrintSettings('account_to_account')}
                        onChange={(e) => {
                          updatePrintSetting(
                            'account_to_account',
                            e.target.checked
                          );
                        }}
                        style={{ border: 'none', margin: 0 }}
                        readOnly={isDisabled}
                      />
                    </div>
                  </div>

                  {/* Show balance cards only when !isDisabled */}
                  {!isDisabled && (
                    <div className="col-xxl-3 col-12">
                      {/* Account Balance Cards */}

                      {showBalances && (
                        <div>
                          {/* Debit Account Balance */}
                          {selectedDebitAccount && (
                            <AccountBalanceCard
                              heading="Debit Account Balance"
                              accountName={selectedDebitAccount.label}
                              balances={debitAccountBalance?.balances || []}
                              loading={debitAccountBalance === undefined}
                            />
                          )}
                          {/* Credit Account Balance */}
                          {selectedCreditAccount && (
                            <AccountBalanceCard
                              heading="Credit Account Balance"
                              accountName={selectedCreditAccount.label}
                              balances={creditAccountBalance?.balances || []}
                              loading={creditAccountBalance === undefined}
                            />
                          )}
                        </div>
                      )}
                      {/* Exchange Rates Card */}
                      {/* <h6 className="mb-2">
                      Live Exchange Rates Against Base Currency
                    </h6>
                      <div className="d-card account-balance-card">
                        <div className="d-flex justify-content-between align-items-center mb-3">
                          <div className="d-flex align-items-center account-name w-100">
                            <span className="me-2" style={{ color: '#6B7280' }}>
                              Inverse
                            </span>
                            <div className="form-check form-switch">
                              <input
                                className="form-check-input"
                                type="checkbox"
                                style={{ cursor: 'pointer' }}
                              />
                            </div>
                          </div>
                        </div>
                        <table className="w-100">
                          <thead>
                            <tr style={{ borderBottom: '1px solid #E5E7EB' }}>
                              <th
                                style={{
                                  padding: '8px 0',
                                  color: '#6B7280',
                                  fontWeight: '500',
                                }}
                              >
                                FCy
                              </th>
                              <th
                                style={{
                                  padding: '8px 0',
                                  color: '#6B7280',
                                  fontWeight: '500',
                                }}
                              >
                                Rates
                              </th>
                              <th
                                style={{
                                  padding: '8px 0',
                                  color: '#6B7280',
                                  fontWeight: '500',
                                }}
                              >
                                Change (24h)
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            {exchangeRatesData?.length > 0 ? (
                              exchangeRatesData.map((rate, index) => (
                                <tr key={index}>
                                  <td style={{ padding: '8px 0' }}>
                                    {rate.currency}
                                  </td>
                                  <td style={{ padding: '8px 0' }}>{rate.rate}</td>
                                  <td
                                    style={{
                                      padding: '8px 0',
                                      color: rate.isPositive
                                        ? '#22C55E'
                                        : '#EF4444',
                                    }}
                                  >
                                    {rate.change}
                                  </td>
                                </tr>
                              ))
                            ) : (
                              <tr>
                                <td colSpan="3" style={{ padding: '8px 0', textAlign: 'center', color: '#6B7280' }}>
                                  {exchangeRatesData ? 'No exchange rates available' : 'Loading...'}
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div> */}

                      <h6 className="mb-2">
                        Live Exchange Rates Against Base Currency
                      </h6>
                      <div className="d-card account-balance-card">
                        <div className="d-flex justify-content-between align-items-center mb-3">
                          <div className="d-flex align-items-center account-name w-100">
                            <span className="me-2" style={{ color: '#6B7280' }}>
                              Inverse
                            </span>
                            <div className="form-check form-switch">
                              <input
                                className="form-check-input"
                                type="checkbox"
                                style={{ cursor: 'pointer' }}
                              />
                            </div>
                          </div>
                        </div>
                        <table className="w-100">
                          <thead>
                            <tr style={{ borderBottom: '1px solid #E5E7EB' }}>
                              <th
                                style={{
                                  padding: '8px 0',
                                  color: '#6B7280',
                                  fontWeight: '500',
                                }}
                              >
                                FCy
                              </th>
                              <th
                                style={{
                                  padding: '8px 0',
                                  color: '#6B7280',
                                  fontWeight: '500',
                                }}
                              >
                                Rates
                              </th>
                              <th
                                style={{
                                  padding: '8px 0',
                                  color: '#6B7280',
                                  fontWeight: '500',
                                }}
                              >
                                Change (24h)
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            {MOCK_EXCHANGE_RATES.map((rate, index) => (
                              <tr key={index}>
                                <td style={{ padding: '8px 0' }}>
                                  {rate.currency}
                                </td>
                                <td style={{ padding: '8px 0' }}>
                                  {rate.rate}
                                </td>
                                <td
                                  style={{
                                    padding: '8px 0',
                                    color: rate.isPositive
                                      ? '#22C55E'
                                      : '#EF4444',
                                  }}
                                >
                                  {rate.change}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>

      <VoucherNavigationBar
        isDisabled={isDisabled}
        actionButtons={[
          { text: 'Save', onClick: handleSubmit },
          {
            text: 'Cancel',
            onClick: handleCancel,
            variant: 'secondaryButton',
          },
        ]}
        loading={createMutation.isPending}
        onAttachmentClick={() => setUploadAttachmentsModal(true)}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />

      {/* Upload Attachements Modal */}
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          uploadOnly
          getUploadedFiles={setAddedAttachments}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>
    </>
  );
};

export default NewAccountToAccount;
