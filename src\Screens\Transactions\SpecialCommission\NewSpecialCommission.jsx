import { Form, Formik } from 'formik';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import useFormStore from '../../../Stores/FormStore';
import { specialCommissionHeaders } from '../../../Utils/Constants/TableHeaders';
import { specialCommissionValidationSchema } from '../../../Utils/Validations/ValidationSchemas';
import SpecialCommissionRow from './SpecialCommissionRow';

const generateInitialRows = (count) => {
  const rows = {};
  Array.from({ length: count }).forEach(() => {
    const id = crypto.randomUUID();
    rows[id] = {
      id,
      ledger: '',
      credit_account: '',
      narration: '',
      percentage: '',
      amount: '',
      isManuallySet: false,
    };
  });
  return rows;
};
const INITIAL_STATE = generateInitialRows(3);

const NewSpecialCommission = ({
  state,
  currencyOptions,
  getAccountsByTypeOptions,
  newlyCreatedAccount,
  setShowAddLedgerModal,
}) => {
  const getValuesObject = () => {
    switch (state?.fromPage) {
      case 'rv':
        return state?.values?.rvValues;
      case 'a2a':
        return state?.values?.a2aValues;
      default:
        return null;
    }
  };
  const navigate = useNavigate();

  const formikRef = useRef();

  const formId = 'special-commission';
  const { saveFormValues, getFormValues, clearFormValues } = useFormStore();
  const valuesFromPreviousPage = getValuesObject();
  const [rows, setRows] = useState(INITIAL_STATE);

  useEffect(() => {
    if (state?.values?.isEdit && formikRef.current) {
      const previousValues = getFormValues(formId);
      formikRef.current.setValues((prev) => {
        return {
          ...prev,
          commission: previousValues?.commission || '',
          total_commission: previousValues?.total_commission || '',
          description: previousValues?.description || '',
        };
      });
      if (previousValues?.distributions?.length > 0) {
        setRows(
          previousValues?.distributions?.map((row) => {
            const id = crypto.randomUUID();
            return {
              id,
              ledger: row.ledger,
              credit_account: row.credit_account,
              narration: row.narration,
              percentage: row.percentage,
              amount: row.amount,
              isManuallySet: false,
            };
          })
        );
      }
    }
  }, [state]);

  const isDisabled = valuesFromPreviousPage?.commission_type === 'Income';

  const [totalCommission, setTotalCommission] = useState(0);
  const [commissionAmount, setCommissionAmount] = useState(0);
  const [difference, setDifference] = useState(0);

  const handleAddRows = () => {
    const newRows = {};
    const id = crypto.randomUUID();
    newRows[id] = {
      id,
      ledger: '',
      credit_account: '',
      narration: '',
      percentage: '',
      amount: '',
      isManuallySet: false,
    };
    setRows((prevRows) => ({ ...prevRows, ...newRows }));
  };

  const handleSubmit = () => {
    const formValues = formikRef.current.values;
    let payload = {
      ...formValues,
    };

    // Remove rows that have empty values
    let distributions = Object.fromEntries(
      Object.entries(rows).filter(([_, obj]) => {
        return Object.entries(obj).every(([key, v]) => {
          // Skip checking narration field
          if (key === 'narration') return true;
          return v !== '' && v !== null && v !== undefined;
        });
      })
    );

    distributions = Object.values(distributions).map(({ id, ...rest }) => rest);

    const transformedDistributions = distributions?.reduce((acc, t, index) => {
      Object.entries(t).forEach(([key, value]) => {
        acc[`distributions[${index}][${key}]`] = value;
      });
      return acc;
    }, {});

    payload = {
      ...payload,
      ...transformedDistributions,
    };

    // Save the form values before navigating
    saveFormValues(formId, {
      ...payload,
      distributions,
    });

    if (state?.pageState === 'edit') {
      navigate('/transactions/receipt-voucher', {
        state: { ...payload, pageState: 'edit', searchTerm: state?.searchTerm },
      });
    } else {
      navigate('/transactions/receipt-voucher', { state: payload });
    }
  };

  const handleCancel = () => {
    navigate('/transactions/receipt-voucher', {
      state: {
        pageState: state.pageState,
        searchTerm: state?.searchTerm,
      },
    });
  };

  const handleDelete = () => {
    // Clear saved form values on successful submission
    clearFormValues('special-commission');
    navigate('/transactions/receipt-voucher', {
      state: {
        pageState: state.pageState,
        searchTerm: state?.searchTerm,
      },
    });
  };

  // Handler functions for rows
  const updateField = useCallback(
    (id, field, value, isManual = false) => {
      setRows((prev) => {
        const newRows = {
          ...prev,
          [id]: {
            ...prev[id],
            [field]: value,
            ...(field === 'percentage' && { isManuallySet: isManual }),
          },
        };

        if (field === 'percentage' && !isDisabled) {
          const rowIds = Object.keys(newRows);
          const currentIndex = rowIds.indexOf(id);
          const nextRowId = rowIds[currentIndex + 1];

          // // Clear next row's values when current row is being edited
          // if (nextRowId) {
          //   newRows[nextRowId] = {
          //     ...newRows[nextRowId],
          //     percentage: '',
          //     amount: '',
          //   };
          // }

          // Calculate total percentage of all rows except the next row
          const totalPercentage = Object.entries(newRows).reduce(
            (acc, [rowId, row]) => {
              if (rowId !== nextRowId) {
                return acc + (parseFloat(row.percentage) || 0);
              }
              return acc;
            },
            0
          );

          const remainingPercentage = 100 - totalPercentage;

          if (
            nextRowId &&
            remainingPercentage > 0 &&
            !newRows[nextRowId].isManuallySet
          ) {
            newRows[nextRowId] = {
              ...newRows[nextRowId],
              percentage: remainingPercentage.toString(),
              amount: (
                (remainingPercentage * commissionAmount) /
                100
              ).toString(),
              isManuallySet: false,
            };
          }

          // Update amounts for all rows
          Object.keys(newRows).forEach((rowId) => {
            const percentage = parseFloat(newRows[rowId].percentage) || 0;
            newRows[rowId] = {
              ...newRows[rowId],
              amount: ((percentage * commissionAmount) / 100).toString(),
            };
          });
        }

        if (field === 'amount') {
          const total = Object.values(newRows).reduce((acc, row) => {
            const amount = parseFloat(row.amount) || 0;
            return acc + amount;
          }, 0);
          setTotalCommission(total);
          setDifference(formikRef.current.values.total_commission - total);
        }
        return newRows;
      });
    },
    [commissionAmount]
  );

  const handleDeleteRow = (id) => {
    setRows((prevRows) => {
      const newState = { ...prevRows };
      delete newState[id];
      return newState;
    });
  };

  useEffect(() => {
    if (commissionAmount && !isDisabled) {
      const firstRowId = Object.keys(rows)[0];
      if (firstRowId) {
        updateField(firstRowId, 'percentage', '100');
        updateField(firstRowId, 'amount', commissionAmount);
      }
      // } else if (!commissionAmount && !isDisabled) {
      //   setRows(INITIAL_STATE);
    }
  }, [commissionAmount]);

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={{
            transaction_no: valuesFromPreviousPage?.current,
            date: valuesFromPreviousPage?.date,
            commission_type: valuesFromPreviousPage?.commission_type,
            ledger: valuesFromPreviousPage?.ledger?.value,
            account_id: valuesFromPreviousPage?.account_id?.value,
            currency_id: valuesFromPreviousPage?.currency?.value,
            amount: valuesFromPreviousPage?.amount,
            commission: '',
            total_commission: '',
            description: '',
          }}
          validationSchema={specialCommissionValidationSchema}
          onSubmit={handleSubmit}
        >
          {({
            values,
            errors,
            touched,
            handleChange,
            handleBlur,
            setFieldValue,
          }) => (
            <Form>
              <div className="row">
                <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-4">
                      <CustomInput
                        name={'transaction_no'}
                        label={'Transaction No'}
                        type={'text'}
                        defaultValue={values.transaction_no}
                        disabled={true}
                        readOnly
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'date'}
                        label={'Date'}
                        type={'date'}
                        defaultValue={values.date}
                        disabled={true}
                        readOnly
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'commission_type'}
                        label={'Commission Type'}
                        placeholder={'Select Commission Type'}
                        options={[
                          {
                            label: 'Income',
                            value: 'Income',
                          },
                          {
                            label: 'Expense',
                            value: 'Expense',
                          },
                        ]}
                        isDisabled={true}
                        value={values.commission_type}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3" />

                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'ledger'}
                        label={'Ledger'}
                        options={[
                          { label: 'PL', value: 'party' },
                          { label: 'GL', value: 'general' },
                          { label: 'WIC', value: 'walkin' },
                        ]}
                        placeholder={'Select Ledger'}
                        value={values.ledger}
                        isDisabled={true}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'account_id'}
                        label={'Account'}
                        isDisabled={true}
                        options={getAccountsByTypeOptions(values.ledger)}
                        placeholder={'Select Account'}
                        value={values.account_id}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'currency_id'}
                        label={'Currency'}
                        isDisabled={true}
                        options={currencyOptions}
                        placeholder={'Select Currency'}
                        value={values.currency_id}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'amount'}
                        label={'Amount'}
                        type={'number'}
                        disabled={true}
                        value={values.amount}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.amount && errors.amount}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'commission'}
                        label={'Commission Percentage'}
                        type={'number'}
                        min={0}
                        max={100}
                        value={values.commission}
                        onChange={(v) => {
                          handleChange(v);
                          setFieldValue(
                            'total_commission',
                            (parseFloat(v.target.value) *
                              parseFloat(values.amount)) /
                              100
                          );
                          setCommissionAmount(
                            (parseFloat(v.target.value) *
                              parseFloat(values.amount)) /
                              100
                          );
                        }}
                        onBlur={handleBlur}
                        error={touched.commission && errors.commission}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'total_commission'}
                        label={'Commission Amount'}
                        type={'number'}
                        value={values.total_commission}
                        onChange={(v) => {
                          handleChange(v);
                          setCommissionAmount(v.target.value);
                          setFieldValue(
                            'commission',
                            (parseFloat(v.target.value) * 100) /
                              parseFloat(values.amount)
                          );
                        }}
                        onBlur={handleBlur}
                        error={
                          touched.total_commission && errors.total_commission
                        }
                      />
                    </div>
                    <div className="col-12 mb-3">
                      <CustomInput
                        name={'description'}
                        label={'Description'}
                        type={'textarea'}
                        value={values.description}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.description && errors.description}
                      />
                    </div>
                  </div>
                </div>

                <CustomTable
                  displayCard={false}
                  headers={specialCommissionHeaders}
                  isPaginated={false}
                  className={'inputTable'}
                  hideSearch
                  hideItemsPerPage
                >
                  <tbody>
                    {Object.values(rows).map((row, index) => (
                      <SpecialCommissionRow
                        key={row.id}
                        row={row}
                        commissionAmount={values.total_commission}
                        isDisabled={isDisabled || !values.total_commission}
                        getAccountsByTypeOptions={getAccountsByTypeOptions}
                        index={index}
                        handleDeleteRow={handleDeleteRow}
                        updateField={updateField}
                        setShowAddLedgerModal={setShowAddLedgerModal}
                      />
                    ))}
                  </tbody>
                </CustomTable>

                <div className="d-flex flex-wrap align-items-end justify-content-between mt-3 mb-5">
                  <div className="d-flex gap-3 flex-wrap">
                    <CustomButton
                      type={'submit'}
                      text="Save"
                      // onClick={handleSubmit}
                    />

                    {!isDisabled && values.total_commission ? (
                      <CustomButton
                        text="Add Rows"
                        type="button"
                        onClick={handleAddRows}
                        variant="secondaryButton"
                      />
                    ) : null}
                    {state?.values?.isEdit && (
                      <CustomButton
                        text="Delete"
                        type="button"
                        onClick={handleDelete}
                        variant="secondaryButton"
                      />
                    )}
                    <CustomButton
                      text="Cancel"
                      type="button"
                      onClick={handleCancel}
                      variant="secondaryButton"
                    />
                  </div>
                  <div className="d-flex flex-column gap-2 mt-1 debit-credit-inputs">
                    <CustomInput
                      name="totalCommission"
                      label={'Total Commission'}
                      labelClass={'fw-medium'}
                      type="number"
                      error={false}
                      borderRadius={10}
                      value={
                        values.commission_type === 'Income'
                          ? values.total_commission
                          : totalCommission.toFixed(2)
                      }
                      readOnly
                    />
                    <CustomInput
                      name="difference"
                      label={'Difference'}
                      labelClass={'fw-medium'}
                      type="number"
                      error={false}
                      borderRadius={10}
                      value={difference.toFixed(2)}
                      readOnly
                    />
                    {difference !== 0 && (
                      <p className="text-danger">Difference must be 0</p>
                    )}
                  </div>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </>
  );
};

export default NewSpecialCommission;
