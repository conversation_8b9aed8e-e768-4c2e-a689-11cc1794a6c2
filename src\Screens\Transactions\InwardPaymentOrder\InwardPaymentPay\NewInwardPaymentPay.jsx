import { Form, Formik } from 'formik';
import React, { useRef, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import AccountBalanceCard from '../../../../Components/AccountBalanceCard/AccountBalanceCard';
import AttachmentsView from '../../../../Components/AttachmentsView/AttachmentsView';
import CustomButton from '../../../../Components/CustomButton';
import CustomCheckbox from '../../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../../Components/CustomInput';
import CustomModal from '../../../../Components/CustomModal';
import SearchableSelect from '../../../../Components/SearchableSelect/SearchableSelect';
import VoucherNavigationBar from '../../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import { mainFormValidationSchema } from '../../../../Utils/Validations/ValidationSchemas';

const NewInwardPaymentPay = ({
  setShowAddLedgerModal,
  newlyCreatedAccount,
  uploadAttachmentsModal,
  setUploadAttachmentsModal,
  selectedFiles,
  setSelectedFiles,
}) => {
  const navigate = useNavigate();
  const formikRef = useRef();
  const [showVatOutOfScopeModal, setShowVatOutOfScopeModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);

  const handleSubmit = () => {
    const formValues = formikRef.current.values;
    console.log('formValues', formValues);
    console.log('selectedFiles', selectedFiles);
    let payload = {
      ...formValues,
      ...selectedFiles,
    };

    if (payload.mode === 'online') {
      setShowPaymentModal(true);
    } else {
      console.log('submit payload:', payload);
    }
  };

  const handleVatOutOfScope = (values) => {
    console.log('handleVatOutOfScope', values);
  };

  const handleCancel = () => {
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
  };

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          validationSchema={mainFormValidationSchema}
          initialValues={{
            debite_note_number: '',
            date: '',
            account: '',
            pay_date: '',
            pay_type: '',
            order_amount: '',
            ref_no: '',
            balance_amount: '',
            beneficiary: '',
            ledger: '',
            contact_no: '',
            vat_type: '',
            nationality: '',
            vat_terms: '',
            id_detail: '',
            settle_date: '',
            place_of_issue: '',
            cheque_number: '',
            sender: '',
            due_date: '',
            sender_nationality: '',
            amount: '',
            origin: '',
            commission: '',
            purpose: '',
            vat_amount: '',
            net_total: '',
            narration: '',
            signature: '',
            mode: '',
            currency: '',
            account_select: '',
          }}
          onSubmit={handleSubmit}
        >
          {({ values, touched, errors, handleChange, handleBlur, setFieldValue }) => (
            <Form>
              <div className="row">
                <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
                  <div className="row mb-4">
                    {/* First Row */}
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="debite_note_number"
                        label="Debite Note Number"
                        placeholder="DN15"
                        value={values.debite_note_number}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="date"
                        label="Date"
                        type="date"
                        value={values.date}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="account"
                        label="Account"
                        placeholder="Enter Account"
                        value={values.account}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>

                    {/* Second Row */}
                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        name="ledger"
                        label="Ledger"
                        options={[
                          { label: 'PL', value: 'pl' },
                          { label: 'GL', value: 'gl' },
                          { label: 'WIC', value: 'wic' }
                        ]}
                        value={values.ledger}
                        onChange={(selected) => {
                          setFieldValue('ledger', selected.value);
                          setFieldValue('account', '');
                        }}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        name="mode"
                        label="Mode"
                        options={[
                          { label: 'Cash', value: 'cash' },
                          { label: 'Online', value: 'online' }
                        ]}
                        value={values.mode}
                        onChange={(selected) => setFieldValue('mode', selected.value)}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        name="account_select"
                        label="Account"
                        options={[
                          ...(values.ledger === 'pl' ? [{ label: 'Add New PL', value: 'add_new_pl' }] : []),
                          ...(values.ledger === 'gl' ? [{ label: 'Add New GL', value: 'add_new_gl' }] : []),
                          ...(values.ledger === 'wic' ? [{ label: 'Add New WIC', value: 'add_new_wic' }] : []),
                          // Add other account options here
                        ]}
                        value={values.account_select}
                        onChange={(selected) => {
                          if (selected.value.startsWith('add_new_')) {
                            setShowAddLedgerModal(selected.value.replace('add_new_', 'add new '));
                          } else {
                            setFieldValue('account_select', selected.value);
                          }
                        }}
                      />
                    </div>
                    {/* Third Row */}
                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        name="pay_type"
                        label="Pay Type"
                        options={[
                          { label: 'PDC', value: 'pdc' }
                          // Add more options as needed
                        ]}
                        value={values.pay_type}
                        onChange={(selected) => setFieldValue('pay_type', selected.value)}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="order_amount"
                        label="Order Amount"
                        placeholder="EUR 5,000.00"
                        value={values.order_amount}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>

                    {/* Fourth Row */}
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="ref_no"
                        label="Ref.No."
                        placeholder="003"
                        value={values.ref_no}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="balance_amount"
                        label="Balance Amount"
                        placeholder="EUR 5,000.00"
                        value={values.balance_amount}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>

                    {/* Fifth Row */}
                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        name="beneficiary"
                        label="Beneficiary"
                        options={[
                          { label: 'MR Kim', value: 'mr_kim' },
                          { label: 'Add New Beneficiary', value: 'add_new_beneficiary' }
                        ]}
                        value={values.beneficiary}
                        onChange={(selected) => {
                          if (
                            selected.label?.toLowerCase()?.startsWith('add new')
                          ) {
                            setShowAddLedgerModal(
                              selected.label?.toLowerCase()
                            );
                          } else {
                            setFieldValue('beneficiary', selected.value);
                          }
                        }}
                        // onChange={(selected) => {
                        //   if (selected.value === 'add_new_beneficiary') {
                        //     setShowBeneficiaryModal(true);
                        //   } else {
                        //     setFieldValue('beneficiary', selected.value);
                        //   }
                        // }}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="contact_no"
                        label="Contact No"
                        placeholder="123456789"
                        value={values.contact_no}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>

                    {/* Sixth Row */}
                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        name="vat_type"
                        label="VAT Type"
                        options={[
                          { label: 'Charge', value: 'charge' }
                          // Add more options as needed
                        ]}
                        value={values.vat_type}
                        onChange={(selected) => setFieldValue('vat_type', selected.value)}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        name="vat_terms"
                        label="VAT Terms"
                        options={[
                          { label: 'Standard Rate(5.00%)', value: 'standard' },
                          { label: 'Out Of Scope', value: 'out_of_scope' }
                        ]}
                        value={values.vat_terms}
                        onChange={(selected) => {
                          if (selected.value === 'out_of_scope') {
                            setShowVatOutOfScopeModal(true);
                          }
                          setFieldValue('vat_terms', selected.value);
                        }}
                      />
                    </div>

                    {/* Additional Fields */}
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="id_detail"
                        label="ID Detail"
                        placeholder="Emirates ID, 12345678, dd/mm/yyyy"
                        value={values.id_detail}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="settle_date"
                        label="Settle Date"
                        type="date"
                        value={values.settle_date}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="place_of_issue"
                        label="Place Of Issue"
                        placeholder="Dubai"
                        value={values.place_of_issue}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        name="cheque_number"
                        label="Cheque Number"
                        options={[
                          { label: 'Select Cheque Number', value: '' }
                          // Add your cheque number options
                        ]}
                        value={values.cheque_number}
                        onChange={(selected) => setFieldValue('cheque_number', selected.value)}
                      />
                    </div>

                    {/* Additional fields from second image */}
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="sender"
                        label="Sender"
                        placeholder="Account ABC"
                        value={values.sender}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="due_date"
                        label="Due Date"
                        type="date"
                        value={values.due_date}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        name="nationality"
                        label="Nationality"
                        options={[
                          { label: 'United Arab Emirates', value: 'uae' }
                          // Add more nationalities
                        ]}
                        value={values.nationality}
                        onChange={(selected) => setFieldValue('nationality', selected.value)}
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        name="origin"
                        label="Origin"
                        options={[
                          { label: 'Select Origin', value: '' }
                          // Add origin options
                        ]}
                        value={values.origin}
                        onChange={(selected) => setFieldValue('origin', selected.value)}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="commission"
                        label="Commission"
                        placeholder="Enter Commission"
                        value={values.commission}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        name="purpose"
                        label="Purpose"
                        options={[
                          { label: 'Select Purpose', value: '' }
                          // Add purpose options
                        ]}
                        value={values.purpose}
                        onChange={(selected) => setFieldValue('purpose', selected.value)}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="vat_amount"
                        label="VAT Amount"
                        placeholder="0.00"
                        value={values.vat_amount}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="net_total"
                        label="Net Total"
                        placeholder="0.00"
                        value={values.net_total}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>

                    {/* Narration and Signature */}
                    <div className="col-12 mb-3">
                      <CustomInput
                        name="narration"
                        label="Narration"
                        type="textarea"
                        rows={4}
                        placeholder="Enter Narration"
                        value={values.narration}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 mb-3">
                      <CustomInput
                        name="signature"
                        label="Signature"
                        type="textarea"
                        rows={4}
                        value={values.signature}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>
                  </div>
                </div>
                <div className="col-0  col-xxl-2" />
                <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
                  <div className="row">
                    {/* Right side cards */}
                    <div
                      className="col-12 mb-5"
                      style={{ maxWidth: '350px' }}
                    >
                      <AccountBalanceCard />
                    </div>
                  </div>
                </div>

                <div className="d-flex flex-wrap justify-content-start mb-5">
                  <div className="d-inline-block mt-3">
                    <CustomCheckbox
                      label="Account Balance"
                      style={{ border: 'none', margin: 0 }}
                      onChange={() => { }}
                    />
                    <CustomCheckbox
                      label="Print"
                      style={{ border: 'none', margin: 0 }}
                    />
                  </div>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
      <VoucherNavigationBar
        actionButtons={[
          { text: 'Save', onClick: handleSubmit },
          { text: 'Cancel', onClick: handleCancel, variant: 'secondaryButton' },
        ]}
        onAttachmentClick={() => setUploadAttachmentsModal(true)}
        lastVoucherHeading="Last PV Number"
        lastVoucherNumber={23}
      />
      {/* Upload Attachements Modal */}
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          uploadOnly
          getUploadedFiles={setSelectedFiles}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>

      {/* VAT Out Of Scope Modal  */}
      <CustomModal
        show={showVatOutOfScopeModal}
        close={() => {
          formikRef.current.values.vat_terms = '';
          setShowVatOutOfScopeModal(false);
        }}
        hideClose={true}
      >
        <div className="text-center mb-3 mt-5">
          <h4 className="modalTitle px-5">Out Of Scope</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{ out_of_scope: '' }}
            // validationSchema={addOfficeLocationValidationSchema}
            onSubmit={handleVatOutOfScope}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    name={'out_of_scope'}
                    type={'textarea'}
                    required
                    label={'Reason'}
                    r
                    rows={1}
                    value={values.out_of_scope}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.out_of_scope && errors.out_of_scope}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {/* {!addOfficeLocationMutation.isPending ? ( */}
                  <>
                    <CustomButton type="submit" text={'Submit'} />
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={() => {
                        setShowVatOutOfScopeModal(false);
                        formikRef.current.values.vat_terms = '';
                      }}
                    />
                  </>
                  {/* ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} */}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>

      {/* Payment Modal */}
      <CustomModal
        show={showPaymentModal}
        close={() => {
          setShowPaymentModal(false);
        }}
        size="lg"
      >
        <div className="text-center mb-3 mt-5">
          <h4 className="modalTitle px-5">Payment</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{
              cardholderName: '',
              number: '',
              cvv: '',
              exp_month: '',
              bank_account_number: '',
              swift_bic_code: '',
              routing_number: '',
              iban: '',
              account_name: '',
            }}
            // validationSchema={paymentValidationSchema}
            onSubmit={() => {
              console.log('Payment');
              setShowPaymentModal(false);
            }}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <Row className="mb-3">
                  <h4 className="screen-title-body">Payer's Detail</h4>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name="cardholderName"
                      label="Cardholder Name"
                      placeholder="Enter Cardholder Name"
                      required
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.cardholderName && errors.cardholderName}
                    />
                  </Col>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name="number"
                      label="Card Number"
                      placeholder="Enter Card Number"
                      required
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.number && errors.number}
                    />
                  </Col>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name="cvv"
                      label="CVV Number"
                      placeholder="Enter CVV"
                      required
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.cvv && errors.cvv}
                    />
                  </Col>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name="exp_month"
                      label="Validaty"
                      placeholder="MM/YYYY"
                      required
                      type="month"
                      min="1"
                      max="12"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.exp_month && errors.exp_month}
                    />
                  </Col>
                </Row>
                <Row>
                  <h4 className="screen-title-body">Receiver's Detail</h4>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name={'bank_account_number'}
                      type={'text'}
                      label={'Bank Account Number'}
                      placeholder={'Enter Bank Account Number'}
                      value={values.bank_account_number}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={
                        touched.bank_account_number &&
                        errors.bank_account_number
                      }
                    />
                  </Col>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name={'swift_bic_code'}
                      type={'text'}
                      label={'SWIFT/BIC Code'}
                      placeholder={'Enter SWIFT Code'}
                      value={values.swift_bic_code}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.swift_bic_code && errors.swift_bic_code}
                    />
                  </Col>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name={'routing_number'}
                      type={'text'}
                      label={'Routing Number'}
                      placeholder={'Enter Routing Number'}
                      value={values.routing_number}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.routing_number && errors.routing_number}
                    />
                  </Col>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name={'iban'}
                      type={'text'}
                      label={'IBAN'}
                      placeholder={'Enter IBAN'}
                      value={values.iban}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.iban && errors.iban}
                    />
                  </Col>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name={'account_name'}
                      type={'text'}
                      label={'Account Name'}
                      placeholder={'Enter Account Name'}
                      value={values.account_name}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.account_name && errors.account_name}
                    />
                  </Col>
                </Row>
                <div className="d-flex justify-content-start gap-3 mt-4">
                  <CustomButton
                    variant="primary"
                    type="submit"
                    text="Pay Now"
                  // loading={paymentMutation.isPending}
                  // disabled={paymentMutation.isPending}
                  />
                  <CustomButton
                    variant="secondaryButton"
                    type="butston"
                    text="Cancel"
                    onClick={() => {
                      setShowPaymentModal(false);
                    }}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </>
  );
};

export default NewInwardPaymentPay;
