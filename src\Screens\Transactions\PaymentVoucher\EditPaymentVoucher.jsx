import { ErrorMessage, Form, Formik } from 'formik';
import React, { useEffect, useRef, useState } from 'react';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomButton from '../../../Components/CustomButton';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import ExchangeRatesCard from '../../../Components/ExchangeRatesCard/ExchangeRatesCard';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';

import {
  MOCK_EXCHANGE_RATES,
  MOCK_PAYMENT_VOUCHER_DATA,
  supportLogsData,
} from '../../../Mocks/MockData';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import useFormStore from '../../../Stores/FormStore.js';
import useSettingsStore from '../../../Stores/SettingsStore.js';
import {
  addPaymentVoucherAttachment,
  deletePaymentVoucherAttachment,
  getBenefeciariesByAccount,
  getPaymentVoucherListing,
  updatePaymentVoucher,
} from '../../../Services/Transaction/PaymentVoucher.js';
import {
  getChequeNumberByBank,
  getCurrencyRate,
} from '../../../Services/Transaction/JournalVoucher.js';
import { showToast } from '../../../Components/Toast/Toast.jsx';
import { isNullOrEmpty, showErrorToast } from '../../../Utils/Utils.jsx';
import SignatureCanvas from 'react-signature-canvas';
import { getBeneficiaryRegisterListing } from '../../../Services/Masters/BeneficiaryRegister.js';
import CombinedInputs from '../../../Components/CombinedInputs/CombinedInputs.jsx';

const EditPaymentVoucher = ({
  // setShowAddLedgerModal,
  // newlyCreatedAccount,
  // uploadAttachmentsModal,
  // setUploadAttachmentsModal,
  // selectedFiles,
  // setSelectedFiles,
  // setPageState,
  date,
  state,
  showModal,
  getCOAAccountsByModeOptions,
  currencyOptions,
  vatData,
  isDisabled = false,
  setIsDisabled,
  searchTerm,
  setPageState,
  setSearchTerm,
  setShowAddLedgerModal,
  setCurrencyToSelect,
  setShowMissingCurrencyRateModal,
  newlyCreatedAccount,
  newlyCreatedBeneficiary,
  lastVoucherNumbers,
  updatePrintSetting,
  uploadAttachmentsModal,
  setUploadAttachmentsModal,
  selectedFiles,
  setSelectedFiles,
  accountData,
  modesData,
  onFormDataChange,
  restoreValuesFromStore,
}) => {
  // const navigate = useNavigate();
  // const formikRef = useRef();
  // const [showVatOutOfScopeModal, setShowVatOutOfScopeModal] = useState(false);

  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const formikRef = useRef();

  // Access the form store
  const {
    saveFormValues,
    getFormValues,
    hasFormValues,
    clearFormValues,
    setLastVisitedPage,
    getLastVisitedPage,
    clearLastVisitedPage,
  } = useFormStore();
  const formId = 'edit-payment-voucher'; // Unique identifier for this form

  // For getting print checkbox state from BE
  const { getPrintSettings } = useSettingsStore();

  const [showVatOutOfScopeModal, setShowVatOutOfScopeModal] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState(null);
  const [selectedLedgerAccount, setSelectedLedgerAccount] = useState(null);
  const [hasShownModal, setHasShownModal] = useState(false);
  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);
  const [outOfScope, setOutOfScope] = useState('');
  const [specialCommissionValues, setSpecialCommissionValues] = useState({});
  const [addedSpecialCommissionValues, setAddedSpecialCommissionValues] =
    useState(null);

  const [voucherDate, setVoucherDate] = useState(
    new Date().toISOString().split('T')[0]
  );
  const [dueDate, setDueDate] = useState('');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedBank, setSelectedBank] = useState(null);
  const [isDueDateEditable, setIsDueDateEditable] = useState(false);
  const [isChequeFieldEnabled, setIsChequeFieldEnabled] = useState(false);
  const [addedAttachments, setAddedAttachments] = useState(null);
  useState(null);

  const apiBaseUrl = import.meta.env.VITE_MILESTONE_BASE_URL;

  const sigCanvas = useRef(null);
  const [trimmedDataURL, setTrimmedDataURL] = useState(null);
  const {
    data: { data: [paymentVoucherData] = [] } = {},
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['paymentVoucher', searchTerm],
    queryFn: () => getPaymentVoucherListing({ search: searchTerm }),
  });

  const { data: currencyRate, isLoading: isLoadingCurrencyRate } = useQuery({
    queryKey: ['pv', 'currencyRate', selectedCurrency],
    queryFn: () => getCurrencyRate(selectedCurrency, date),
    enabled: !!selectedCurrency,
    retry: 1,
    staleTime: 0, // Mark data as stale immediately after fetching
    gcTime: 0, // Remove data from cache immediately after becoming unused
    refetchOnMount: true,
  });

  function clear() {
    sigCanvas.current.clear();
  }

  function trim() {
    setTrimmedDataURL(sigCanvas.current.toDataURL());
  }

  // Load saved form if present
  useEffect(() => {
    const lastPage = getLastVisitedPage(formId);

    if (
      lastPage === 'special-commission' &&
      hasFormValues(formId) &&
      formikRef.current
    ) {
      const savedValues = getFormValues(formId);
      let specialCommissionData = { ...savedValues };

      specialCommissionData.ledger = [
        { label: 'PL', value: 'party' },
        { label: 'GL', value: 'general' },
        { label: 'WIC', value: 'walkin' },
      ].find((x) => savedValues.ledger == x.value);
      specialCommissionData.account_id = getAccountsByTypeOptions(
        specialCommissionData?.ledger?.value
      ).find((x) => x.value == savedValues?.account_id);

      specialCommissionData.currency = currencyOptions.find(
        (x) => x.value == savedValues?.currency_id
      );

      setSpecialCommissionValues(specialCommissionData);
      formikRef.current.setValues(savedValues);
      setIsDisabled(false);
    } else if (lastPage !== 'special-commission') {
      if (hasFormValues('special-commission')) {
        setAddedSpecialCommissionValues(getFormValues('special-commission'));
      }
    }
    // Clear lastVisitedPage so it doesn't persist beyond one use
    clearLastVisitedPage(formId);
  }, []);

  // Update special commission values when payment voucher data changes
  useEffect(() => {
    const lastPage = getLastVisitedPage(formId);
    let pv = paymentVoucherData?.payment_vouchers;
    if (pv) {
      setOutOfScope(pv?.out_of_scope);
      setSpecialCommissionValues({
        ledger: {
          value: pv.ledger,
          label:
            pv.ledger === 'party'
              ? 'PL'
              : pv.ledger === 'general'
              ? 'GL'
              : 'WIC',
        },
        account: {
          value: pv.account_id,
          label: pv.account_name || '',
        },
        amount: pv.amount,
        currency: {
          value: pv.amount_account_id?.id,
          label: pv.amount_account_id?.currency_code || '',
        },
        commission_type: {
          value: pv.commission_type,
          label: pv.commission_type,
        },
        date: pv.date,
      });

      if (pv?.special_commission && lastPage !== 'special-commission') {
        // Save the SC form values
        saveFormValues('special-commission', {
          ...pv?.special_commission,
          ledger: pv?.special_commission?.account_type,
          distributions: [...pv?.special_commission?.commission_distribution],
        });
      }
    }
  }, [paymentVoucherData]);

  useEffect(() => {
    if (lastVoucherNumbers?.current) {
      setSpecialCommissionValues((prev) => ({
        ...prev,
        current: lastVoucherNumbers?.current,
      }));
    }
    if (date) {
      setSpecialCommissionValues((prev) => ({
        ...prev,
        date: date,
      }));
      // Set due_date to date if mode is Bank
      if (formikRef.current?.values.mode === 'Bank') {
        formikRef.current.setFieldValue('due_date', date);
      }
    }
  }, [lastVoucherNumbers?.current, date]);

  // Get Benefeciaries from selected Ledger+Account
  const {
    data: beneficiaryAccounts,
    isLoading: isLoadingBeneficiary,
    isError: isErrorBeneficiary,
    error: errorBeneficiary,
  } = useQuery({
    queryKey: ['beneficiaries', selectedLedgerAccount],
    queryFn: () => getBenefeciariesByAccount(selectedLedgerAccount),
    enabled: !!selectedLedgerAccount,
  });

  // Make options array from the benfeciary queries call
  const getBeneficiaryOptions = (account_id) => {
    if (!account_id) {
      return [{ label: 'Select Ledger', value: null, isDisabled: true }];
    }

    const data = beneficiaryAccounts;
    const loading = isLoadingBeneficiary;
    const error = isErrorBeneficiary;
    const errorMessage = errorBeneficiary;

    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (error) {
      console.error('Unable to fetch beneficiaries', errorMessage);
      return [{ label: 'Unable to fetch beneficiaries', value: null }];
    }
    let options =
      data?.map((x) => ({
        value: x?.id,
        label: x?.title,
      })) || [];

    options.push({
      label: `Add New Beneficiary`,
      value: null,
    });

    return options;
  };

  // Show Missing currency rate modal if rate not present
  useEffect(() => {
    if (
      selectedCurrency &&
      currencyRate &&
      !currencyRate?.rate &&
      !hasShownModal
    ) {
      formikRef.current.setFieldValue('currency_id', '');
      setCurrencyToSelect(selectedCurrency);
      setShowMissingCurrencyRateModal(true);
      setHasShownModal(true);
    }
  }, [selectedCurrency, currencyRate?.rate, hasShownModal]);

  const handleCancel = () => {
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
    // Clear saved form values when resetting
    clearFormValues(formId);
    clearFormValues('special-commission');
    setAddedSpecialCommissionValues(null);
    setPageState('view');
  };

  const updatePaymentVoucherMutation = useMutation({
    mutationFn: ({ searchTerm, payload }) =>
      updatePaymentVoucher(searchTerm, payload),
    onSuccess: (data) => {
      showToast('Payment Voucher Updated!', 'success');
      if (getPrintSettings('payment_voucher')) {
        if (data?.detail?.pdf_url) {
          window.open(data.detail.pdf_url, '_blank');
        }
      }
      queryClient.invalidateQueries(['paymentVoucherListing']);
      queryClient.invalidateQueries(['paymentVoucher', searchTerm]);
      handleCancel();
    },
    onError: (error) => {
      console.error('Error creating Payment Voucher', error);
      if (
        error.message.toLowerCase() ==
        'payment voucher limit reached for this branch.'
      ) {
        showModal(
          'Cannot Create',
          'The maximum number of payment vouchers has been reached. To create new transactions, please increase the transaction number count in the Transaction Number Register.',
          null,
          'error'
        );
      } else {
        showErrorToast(error);
      }
    },
  });

  const getAccountsByTypeOptions = (accountType) => {
    //console.log(accountType);
    // console.log(accountData);

    if (!accountType) {
      return [{ label: 'Select Ledger', value: null, isDisabled: true }];
    }

    const { data, loading, error, errorMessage } =
      accountData[accountType] || {};

    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    let options =
      data?.map((x) => ({
        value: x?.id,
        label: x?.title,
      })) || [];
    switch (accountType) {
      case 'party':
        options.push({
          label: `Add New PL`,
          value: null,
        });
        break;
      case 'general':
        options.push({
          label: `Add New GL`,
          value: null,
        });
        break;
      case 'walkin':
        options.push({
          label: `Add New WIC`,
          value: null,
        });
        break;
      default:
        break;
    }
    return options;
  };

  const getAccountsByTypeMode = (mode) => {
    if (!mode) {
      return [{ label: 'Select Mode', value: null, isDisabled: true }];
    }

    const { data, loading, error, errorMessage } = modesData[mode] || {};

    console.log(modesData[mode]);

    if (mode === 'bank') {
      setDueDate(voucherDate);
      setIsDueDateEditable(true);
      setIsChequeFieldEnabled(true);
    } else if (mode === 'pdc') {
      const tomorrow = new Date(voucherDate);
      tomorrow.setDate(tomorrow.getDate() + 1);
      setDueDate(tomorrow.toISOString().split('T')[0]);
      setIsDueDateEditable(true);

      setIsChequeFieldEnabled(true);
    } else {
      setDueDate('');
      setIsDueDateEditable(false);
      setIsChequeFieldEnabled(false);
    }

    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (error) {
      console.error('Unable to fetch Mode', errorMessage);
      return [{ label: 'Unable to fetch Mode', value: null }];
    }
    return (
      data?.map((x) => ({
        value: x?.id,
        label: x?.account_name,
      })) || []
    );
  };

  const handleSubmit = async () => {
    if (!formikRef.current) return;
    // Validate the form
    const errors = await formikRef.current.validateForm();
    if (Object.keys(errors).length > 0) {
      // Mark all fields as touched to show errors
      formikRef.current.setTouched(
        Object.keys(errors).reduce((acc, key) => {
          acc[key] = true;
          return acc;
        }, {})
      );
      return; // Do not submit if there are errors
    }
    const formValues = formikRef.current.values;
    let payload = {
      date,
      ...formValues,
      ...(formValues.vat_terms.startsWith('A small popup') && {
        out_of_scope_reason: outOfScope,
      }),
      mode: formValues.mode.charAt(0).toUpperCase() + formValues.mode.slice(1),
      vat_amount: vatData.vatType?.vat_percentage
        ? (formValues.amount * vatData.vatType?.vat_percentage) / 100
        : !isNaN(formValues.vat_terms)
        ? (formValues.amount * formValues.vat_terms) / 100
        : '',
      ...(addedSpecialCommissionValues
        ? { special_commission: addedSpecialCommissionValues }
        : {}),
    };

    console.log(payload);

    updatePaymentVoucherMutation.mutate({ searchTerm, payload });
  };
  const handleVatOutOfScope = (values) => {
    setOutOfScope(values.out_of_scope);
    setShowVatOutOfScopeModal(false);
  };
  const handleNavigateToSpecialCommissionPage = () => {
    // Check if required fields are filled
    const requiredFields = [
      'ledger',
      'account_id',
      'amount',
      'currency_id',
      // 'commission_type',
    ];
    const missingFields = requiredFields.filter(
      (field) => !formikRef.current.values[field]
    );
    if (missingFields.length > 0) {
      // Set touched for all required fields to show errors
      const touchedFields = {};
      requiredFields.forEach((field) => {
        touchedFields[field] = true;
      });
      formikRef.current.setTouched({
        ...formikRef.current.touched,
        ...touchedFields,
      });
      return;
    }
    // Todo

    // If all required fields are filled, proceed
    if (formikRef.current && !isDisabled) {
      saveFormValues(formId, formikRef.current.values);
      setLastVisitedPage(formId, 'special-commission');
    }

    // Check if we already have special commission data
    const hasExistingCommission = !!addedSpecialCommissionValues;

    setPageState('edit');
    navigate('/transactions/special-commission', {
      state: {
        fromPage: 'pv',
        pageState: 'edit',
        searchTerm: searchTerm,
        values: {
          pvValues: specialCommissionValues,
          isEdit: hasExistingCommission,
        },
      },
    });
  };

  let getBeneficiariesData = [];

  const {
    data: modeBeneficiaries,
    isLoading: isLoadingBeneficiaries,
    isError: isErrorBeneficiaries,
    error: errorBeneficiaries,
  } = useQuery({
    queryKey: ['per_page', 50],
    queryFn: getBeneficiaryRegisterListing,
    staleTime: 1000 * 60 * 5,
  });

  // Defensive check depending on API structure
  if (Array.isArray(modeBeneficiaries)) {
    getBeneficiariesData = modeBeneficiaries.map((ben) => ({
      label: ben.account,
      value: ben.id,
    }));
  } else if (Array.isArray(modeBeneficiaries?.data)) {
    getBeneficiariesData = modeBeneficiaries.data.map((ben) => ({
      label: ben.account,
      value: ben.id,
    }));
  }

  useEffect(() => {
    modeBeneficiaries;
  }, [modeBeneficiaries]);

  console.log(getBeneficiariesData);

  const {
    data: modeCheques,
    isLoading: isLoadingCheques,
    isError: isErrorCheques,
    error: errorCheques,
  } = useQuery({
    queryKey: ['bank_id', selectedBank],
    queryFn: () => getChequeNumberByBank(selectedBank),
    staleTime: 1000 * 60 * 5,
  });

  console.log(modeCheques);

  const chequeOptions =
    modeCheques?.map((cheque) => ({
      label: cheque.cheque_number, // adjust this based on your API response
      value: cheque.cheque_number,
    })) || [];

  useEffect(() => {
    modeCheques;
  }, [modeCheques]);

  const getVATTermsOptions = () => {
    if (vatData.isLoadingVatType)
      return [
        {
          label: 'Loading...',
          value: '',
        },
      ];
    if (vatData.isErrorVatType) {
      console.error('Unable to fetch VAT Terms', vatData.errorMessage);
      return [{ label: 'Unable to fetch VAT Terms', value: null }];
    }
    return vatData?.vatType?.vats?.map((item) => ({
      label: `${item.title}${
        !isNaN(parseFloat(item.percentage)) ? ' - ' + item.percentage + '%' : ''
      }`,
      value: item.percentage,
    }));
  };

  if (isError) {
    console.log(error);
  }

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={{
            ledger: paymentVoucherData?.payment_vouchers.ledger || '',
            account_id: paymentVoucherData?.payment_vouchers.account_id || '',
            paid_to_id: paymentVoucherData?.payment_vouchers.paid_to?.id || '',
            mode: paymentVoucherData?.payment_vouchers.mode || '',
            mode_account_id:
              paymentVoucherData?.payment_vouchers.mode_account_id?.id || '',
            party_bank: paymentVoucherData?.payment_vouchers.party_bank || '',
            cheque_number:
              paymentVoucherData?.payment_vouchers.cheque_number || '',
            due_date: paymentVoucherData?.payment_vouchers.due_date || '',
            narration: paymentVoucherData?.payment_vouchers.narration || '',

            amount: paymentVoucherData?.payment_vouchers.amount || '',
            currency_id: paymentVoucherData?.payment_vouchers.currency_id || '',
            commission_type:
              paymentVoucherData?.payment_vouchers.commission_type || '',
            commission: paymentVoucherData?.payment_vouchers.commission || '',
            commission_percentage:
              paymentVoucherData?.payment_vouchers.commission || '',
            commission_amount:
              paymentVoucherData?.payment_vouchers.commission_amount || '',
            vat_terms: paymentVoucherData?.payment_vouchers.vat_terms || '',
            vat_percentage:
              paymentVoucherData?.payment_vouchers.vat_percentage || '',
            vat_amount: paymentVoucherData?.payment_vouchers.vat_amount || '',
            net_total: paymentVoucherData?.payment_vouchers.net_total || '',
            comment: paymentVoucherData?.payment_vouchers.comment || '',
            signature: paymentVoucherData?.payment_vouchers.signature_url,
          }}
          validate={(values) => {
            const errors = {};

            // Required fields for special commission
            if (!values.ledger) errors.ledger = 'Ledger is required';
            if (!values.account_id) errors.account_id = 'Account is required';
            if (!values.amount) errors.amount = 'Amount is required';
            if (!values.currency_id)
              errors.currency_id = 'Currency is required';
            // if (!values.commission_type)
            //   errors.commission_type = 'Commission Type is required';

            return errors;
          }}
        >
          {({
            values,
            touched,
            errors,
            handleChange,
            handleBlur,
            setFieldValue,
          }) => {
            // --- VAT Calculation useEffect inside Formik render ---
            React.useEffect(() => {
              let commissionAmount =
                addedSpecialCommissionValues?.total_commission ??
                values.commission_amount;

              let vatPercentage =
                vatData.vatType?.vat_percentage ||
                (!isNaN(values.vat_terms) ? values.vat_terms : 0);

              let vatAmount =
                commissionAmount && vatPercentage
                  ? (commissionAmount * vatPercentage) / 100
                  : '';

              setFieldValue('vat_amount', vatAmount);
            }, [
              addedSpecialCommissionValues?.total_commission,
              values.commission_amount,
              values.vat_terms,
              vatData.vatType?.vat_percentage,
              setFieldValue,
            ]);
            // --- End VAT Calculation useEffect ---
            return (
              <Form>
                <div className="row">
                  <div className="col-12 col-xxl-9">
                    <div className="row mb-4">
                      <div className="col-12 col-sm-6 mb-45">
                        <div className="combined-select-container">
                          <label className="mainLabel">Ledger</label>
                          <div className={`combined-select-input`}>
                            <div className="combined-select-left">
                              <SearchableSelect
                                name={'ledger'}
                                className="ledger-select__control"
                                options={[
                                  { label: 'PL', value: 'party' },
                                  { label: 'GL', value: 'general' },
                                  { label: 'WIC', value: 'walkin' },
                                ]}
                                placeholder={'Ledger'}
                                value={values.ledger}
                                onChange={(selected) => {
                                  setFieldValue('ledger', selected.value);
                                }}
                                onBlur={handleBlur}
                              />
                            </div>
                            <div className="separator-between-selects">|</div>
                            <div className="combined-select-right">
                              <SearchableSelect
                                name={'account_id'}
                                className={'ledger-select__control'}
                                options={getAccountsByTypeOptions(
                                  values.ledger
                                )}
                                isDisabled={isDisabled}
                                placeholder={'Select Account'}
                                value={values.account_id}
                                onChange={(selected) => {
                                  if (
                                    selected.label
                                      ?.toLowerCase()
                                      ?.startsWith('add new')
                                  ) {
                                    setShowAddLedgerModal(
                                      selected.label?.toLowerCase()
                                    );
                                  } else {
                                    setFieldValue('account_id', selected.value);
                                    setSelectedLedgerAccount(selected.value);
                                  }
                                }}
                                onBlur={handleBlur}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="col-12 col-sm-6 mb-30">
                        {/*<SearchableSelect*/}
                        {/*  name={'paid_to'}*/}
                        {/*  label={'Paid To'}*/}
                        {/*  options={getBeneficiariesData}*/}
                        {/*  isDisabled={isDisabled}*/}
                        {/*  placeholder={'Select Paid To'}*/}
                        {/*  value={values.paid_to_id}*/}
                        {/*  onChange={(selected) => {*/}
                        {/*    if (*/}
                        {/*      selected.label?.toLowerCase()?.startsWith('add new')*/}
                        {/*    ) {*/}
                        {/*      setShowAddLedgerModal(*/}
                        {/*        selected.label?.toLowerCase(),*/}
                        {/*      );*/}
                        {/*    } else {*/}
                        {/*      setFieldValue('paid_to', selected.value);*/}
                        {/*      setFieldValue('paid_to_id', selected.value);*/}
                        {/*    }*/}
                        {/*  }}*/}
                        {/*  onBlur={handleBlur}*/}
                        {/*/>*/}
                        <SearchableSelect
                          name={'paid_to'}
                          label={'Paid To'}
                          options={getBeneficiaryOptions(values.account_id)}
                          isDisabled={isDisabled}
                          placeholder={'Select Paid To'}
                          value={values.paid_to || newlyCreatedBeneficiary?.id}
                          onChange={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                            } else {
                              setFieldValue('paid_to', selected.value);
                              setFieldValue('paid_to_id', selected.value);
                            }
                          }}
                          onBlur={handleBlur}
                        />
                      </div>
                      {/* MergeSelect */}
                      <div className="col-12 col-sm-6 mb-45">
                        <div className="combined-select-container">
                          <label className="mainLabel">Mode</label>
                          <div className={`combined-select-input`}>
                            <div className="combined-select-left">
                              <SearchableSelect
                                name={'mode'}
                                className={'mode-select__control'}
                                options={[
                                  {
                                    label: 'Cash',
                                    value: 'cash',
                                  },
                                  {
                                    label: 'Bank',
                                    value: 'bank',
                                  },
                                  {
                                    label: 'PDC',
                                    value: 'pdc',
                                  },
                                  {
                                    label: 'Online',
                                    value: 'online',
                                  },
                                ]}
                                isDisabled={isDisabled}
                                placeholder={'Mode'}
                                value={values.mode.toLowerCase()}
                                onChange={(selected) => {
                                  setFieldValue('mode', selected.value);
                                }}
                                onBlur={handleBlur}
                              />
                            </div>
                            <div className="separator-between-selects">|</div>
                            <div className="combined-select-right">
                              <SearchableSelect
                                name={'mode_account_id'}
                                className={'account-select__control'}
                                options={getAccountsByTypeMode(values.mode)}
                                isDisabled={isDisabled}
                                placeholder={'Select Account'}
                                value={values.mode_account_id}
                                onChange={(selected) => {
                                  setFieldValue(
                                    'mode_account_id',
                                    selected.value
                                  );
                                  setSelectedBank(selected.value);
                                  console.log(selectedBank);
                                }}
                                onBlur={handleBlur}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'cheque_number'}
                          label={'Cheque Number'}
                          options={chequeOptions}
                          isDisabled={!isChequeFieldEnabled}
                          placeholder={'Select Cheque Number'}
                          value={values.cheque_number}
                          onChange={(selected) => {
                            setFieldValue('cheque_number', selected.value);
                          }}
                          onBlur={handleBlur}
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name={'due_date'}
                          label={'Due Date'}
                          type={'date'}
                          disabled={!isDueDateEditable}
                          value={dueDate}
                          min={
                            values.mode === 'pdc'
                              ? new Date(
                                  new Date(voucherDate).setDate(
                                    new Date(voucherDate).getDate() + 1
                                  )
                                )
                                  .toISOString()
                                  .split('T')[0]
                              : undefined
                          }
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={touched.due_date && errors.due_date}
                        />
                      </div>
                      <div className="col-12 mb-3">
                        <CustomInput
                          name={'narration'}
                          label={'Narration'}
                          type={'textarea'}
                          rows={4}
                          placeholder={'Enter Narration'}
                          value={values.narration}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={touched.narration && errors.narration}
                        />
                      </div>
                      {/* MergeSelect */}

                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="Currency"
                          type1="select"
                          type2="input"
                          name1="currency_id"
                          name2="amount"
                          value1={values.currency_id}
                          value2={values.amount}
                          options1={currencyOptions}
                          isDisabled={isDisabled}
                          handleBlur={handleBlur}
                          placeholder1="Select Currency"
                          placeholder2="Enter Amount"
                          inputType2="number"
                          className1="currency"
                          className2="amount"
                          onChange1={(selected) => {
                            setSelectedCurrency(selected.value);
                            setHasShownModal(false);
                            setFieldValue('currency_id', selected.value);
                            setSpecialCommissionValues((prev) => ({
                              ...prev,
                              currency: selected,
                            }));
                          }}
                          onChange2={(e) => {
                            handleChange(e);
                            let commission = parseFloat(values.commission || 0);
                            let amount = parseFloat(e.target.value || 0);
                            let commissionAmount = (commission / 100) * amount;
                            let vat = vatData.vatType?.vat_percentage
                              ? (commissionAmount *
                                  vatData.vatType?.vat_percentage) /
                                100
                              : !isNaN(values.vat_terms)
                              ? (commissionAmount * values.vat_terms) / 100
                              : 0;

                            let value =
                              Math.round(
                                (amount +
                                  commissionAmount +
                                  vat +
                                  Number.EPSILON) *
                                  1000000
                              ) / 1000000;
                            setFieldValue('net_total', value);
                            setSpecialCommissionValues((prev) => ({
                              ...prev,
                              amount,
                            }));
                          }}
                          additionalProps={{
                            isLoadingCurrencyRate: isLoadingCurrencyRate,
                          }}
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'commission_type'}
                          label={'Commission Type'}
                          options={[
                            {
                              label: 'Income',
                              value: 'Income',
                            },
                            {
                              label: 'Expense',
                              value: 'Expense',
                            },
                          ]}
                          isDisabled={isDisabled}
                          placeholder={'Select Commission Type'}
                          value={values.commission_type}
                          onChange={(selected) => {
                            setFieldValue('commission_type', selected.value);
                          }}
                          onBlur={handleBlur}
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="Commission Percentage"
                          type1="input"
                          type2="input"
                          name1="commission"
                          name2="commission_amount"
                          value1={values.commission}
                          value2={values.commission_amount}
                          isDisabled={
                            isDisabled ||
                            addedSpecialCommissionValues?.total_commission
                          }
                          handleBlur={handleBlur}
                          placeholder1="Enter Commission %"
                          placeholder2="Commission Amount"
                          inputType1="number"
                          inputType2="text"
                          className1="commission"
                          className2="commission-amount"
                          inputProps1={{
                            min: 0,
                            max: 100,
                          }}
                          inputProps2={
                            {
                              // readOnly: true,
                            }
                          }
                          onChange1={(v) => {
                            handleChange(v);
                            if (v.target.value < 0) {
                              return;
                            }
                            let commission = parseFloat(v.target.value || 0);
                            let amount = parseFloat(values.amount || 0);
                            let commissionAmount = (commission / 100) * amount;
                            let vat = vatData.vatType?.vat_percentage
                              ? (commissionAmount *
                                  vatData.vatType?.vat_percentage) /
                                100
                              : !isNaN(values.vat_terms)
                              ? (commissionAmount * values.vat_terms) / 100
                              : 0;

                            setFieldValue(
                              'net_total',
                              Math.round(
                                (amount +
                                  commissionAmount +
                                  vat +
                                  Number.EPSILON) *
                                  1000000
                              ) / 1000000
                            );
                            setFieldValue(
                              'commission_amount',
                              commissionAmount
                            );
                          }}
                          onChange2={(e) => {
                            handleChange(e);
                            let commissionAmount = parseFloat(
                              e.target.value || 0
                            );
                            let amount = parseFloat(values.amount || 0);
                            let commission =
                              amount !== 0
                                ? (commissionAmount / amount) * 100
                                : 0;
                            let vat = vatData.vatType?.vat_percentage
                              ? (commissionAmount *
                                  vatData.vatType?.vat_percentage) /
                                100
                              : !isNaN(values.vat_terms)
                              ? (commissionAmount * values.vat_terms) / 100
                              : 0;

                            setFieldValue(
                              'net_total',
                              Math.round(
                                (amount +
                                  commissionAmount +
                                  vat +
                                  Number.EPSILON) *
                                  1000000
                              ) / 1000000
                            );
                            setFieldValue('commission', commission);
                          }}
                        />
                      </div>

                      {vatData?.vatType?.vat_type === 'variable' && (
                        <div className="col-12 col-sm-6 mb-45">
                          <SearchableSelect
                            name={'vat_terms'}
                            label={'VAT %'}
                            options={getVATTermsOptions()}
                            isDisabled={isDisabled}
                            placeholder={'Select VAT %'}
                            value={values.vat_terms}
                            onChange={(selected) => {
                              if (
                                selected.value.startsWith(
                                  'A small popup will appear'
                                )
                              ) {
                                setShowVatOutOfScopeModal(true);
                              } else {
                                let commission = parseFloat(
                                  values.commission || 0
                                );
                                let amount = parseFloat(
                                  values.amount ??
                                    getFormValues('special-commission')
                                      ?.total_commission ??
                                    0
                                );
                                let commissionAmount =
                                  (commission / 100) * amount;
                                let vat = vatData.vatType?.vat_percentage
                                  ? (commissionAmount *
                                      vatData.vatType?.vat_percentage) /
                                    100
                                  : !isNaN(selected.value)
                                  ? (commissionAmount * selected.value) / 100
                                  : '';

                                setFieldValue(
                                  'net_total',
                                  Math.round(
                                    (amount +
                                      commissionAmount +
                                      (vat || 0) +
                                      Number.EPSILON) *
                                      1000000
                                  ) / 1000000
                                );
                              }
                              setFieldValue('vat_terms', selected.value);
                            }}
                            onBlur={handleBlur}
                          />
                        </div>
                      )}
                      {vatData?.vatType?.vat_type === 'fixed' && (
                        <div className="col-12 col-sm-6 mb-3">
                          <CustomInput
                            name={'vat_percentage'}
                            label={'VAT %'}
                            type={'number'}
                            disabled={true}
                            placeholder={'Enter VAT Percentage'}
                            value={
                              vatData.vatType?.vat_percentage
                                ? vatData.vatType?.vat_percentage
                                : !isNaN(values.vat_terms)
                                ? values.vat_terms
                                : ''
                            }
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </div>
                      )}
                      <div className="col-12 col-sm-6 mb-45">
                        <CustomInput
                          name={'vat_amount'}
                          label={'VAT Amount'}
                          type={'text'}
                          disabled={true}
                          placeholder={'Enter VAT Amount'}
                          value={
                            vatData.isLoadingVatType
                              ? 'Loading...'
                              : (() => {
                                  const commissionAmount =
                                    addedSpecialCommissionValues?.total_commission ??
                                    values.commission_amount;
                                  const vatPercentage =
                                    vatData.vatType?.vat_percentage ||
                                    (!isNaN(values.vat_terms)
                                      ? values.vat_terms
                                      : 0);
                                  return commissionAmount && vatPercentage
                                    ? (commissionAmount * vatPercentage) / 100
                                    : '';
                                })()
                          }
                          onBlur={handleBlur}
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-45">
                        <CustomInput
                          name={'net_total'}
                          label={'Net Total'}
                          type={'number'}
                          disabled={true}
                          placeholder={'Enter Net Total'}
                          value={values.net_total}
                          onChange={handleChange}
                          onBlur={handleBlur}
                        />
                      </div>

                      <div className="col-12 mb-3">
                        <CustomInput
                          name={'comment'}
                          label={'Comment'}
                          type={'textarea'}
                          rows={4}
                          placeholder={'Enter Comment'}
                          value={values.comment}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={touched.comment && errors.comment}
                        />
                      </div>
                      <div className="col-12 mb-3">
                        <label>Signature</label>
                        <SignatureCanvas
                          ref={sigCanvas}
                          penColor="green"
                          canvasProps={{
                            width: 500,
                            height: 200,
                            className: 'sigCanvas',
                          }}
                        />
                        <div>
                          <button onClick={clear}>Clear</button>
                          <button onClick={trim}>Trim</button>
                        </div>
                        {trimmedDataURL ? (
                          <img alt="signature" src={trimmedDataURL} />
                        ) : null}

                        <img
                          src={values.signature}
                          alt="Signature"
                          style={{ maxWidth: '100%', height: 'auto' }}
                        />
                      </div>
                    </div>
                    <div className="d-flex mb-5">
                      <CustomButton
                        type={'button'}
                        onClick={handleNavigateToSpecialCommissionPage}
                        text={`${
                          !!addedSpecialCommissionValues ? 'Edit' : 'Add'
                        } Special Commission`}
                        disabled={!!values.commission || isDisabled}
                      />
                    </div>
                  </div>
                  <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
                    <div className="row">
                      {/* Right side cards */}
                      <div
                        className="col-12 mb-5"
                        style={{ maxWidth: '350px' }}
                      >
                        <AccountBalanceCard />
                        <ExchangeRatesCard rates={MOCK_EXCHANGE_RATES} />
                      </div>
                    </div>
                  </div>

                  <div className="d-flex flex-wrap justify-content-start mb-5">
                    <div className="d-inline-block mt-3">
                      <CustomCheckbox
                        label="Account Balance"
                        style={{ border: 'none', margin: 0 }}
                        onChange={() => {}}
                      />
                      <CustomCheckbox
                        label="Print"
                        style={{ border: 'none', margin: 0 }}
                      />
                    </div>
                  </div>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>

      <VoucherNavigationBar
        isDisabled={isDisabled}
        actionButtons={[
          { text: 'Save', onClick: handleSubmit },
          {
            text: 'Cancel',
            onClick: handleCancel,
            variant: 'secondaryButton',
          },
        ]}
        loading={updatePaymentVoucherMutation.isPending}
        onAttachmentClick={() => setShowAttachmentsModal(true)}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />

      {/*<VoucherNavigationBar*/}
      {/*  actionButtons={[*/}
      {/*    { text: 'Update', onClick: handleSubmit },*/}
      {/*    { text: 'Cancel', onClick: handleCancel, variant: 'secondaryButton' },*/}
      {/*  ]}*/}
      {/*  onAttachmentClick={() => setUploadAttachmentsModal(true)}*/}
      {/*  lastVoucherHeading="Last PV Number"*/}
      {/*  lastVoucherNumber={23}*/}
      {/*/>*/}
      {/* Upload Attachements Modal */}
      {/*<CustomModal*/}
      {/*  show={uploadAttachmentsModal}*/}
      {/*  close={() => setUploadAttachmentsModal(false)}*/}
      {/*  background={true}*/}
      {/*>*/}
      {/*  <AttachmentsView*/}
      {/*    items={supportLogsData[0]}*/}
      {/*    closeUploader={() => setUploadAttachmentsModal(false)}*/}
      {/*  />*/}
      {/*</CustomModal>*/}

      <CustomModal
        show={showAttachmentsModal}
        close={() => setShowAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          showModal={showAttachmentsModal}
          closeModal={() => setShowAttachmentsModal(false)}
          item={paymentVoucherData}
          deleteSepvice={deletePaymentVoucherAttachment}
          uploadSepvice={addPaymentVoucherAttachment}
          closeUploader={() => setShowAttachmentsModal(false)}
          voucherAttachment={true}
          queryToInvalidate={['paymentVoucher', searchTerm]}
        />
      </CustomModal>

      {/* VAT Out Of Scope Modal  */}
      <CustomModal
        show={showVatOutOfScopeModal}
        close={() => {
          formikRef.current.values.vat_terms = '';
          setShowVatOutOfScopeModal(false);
        }}
        hideClose={true}
      >
        <div className="text-center mb-3 mt-5">
          <h4 className="modalTitle px-5">Out Of Scope</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{ out_of_scope: outOfScope }}
            onSubmit={handleVatOutOfScope}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    name={'out_of_scope'}
                    type={'textarea'}
                    required
                    label={'Reason'}
                    r
                    rows={1}
                    value={values.out_of_scope}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.out_of_scope && errors.out_of_scope}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {/* {!addOfficeLocationMutation.isPending ? ( */}
                  <>
                    <CustomButton type="submit" text={'Submit'} />
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={() => {
                        setShowVatOutOfScopeModal(false);
                        formikRef.current.values.vat_terms = '';
                      }}
                    />
                  </>
                  {/* ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} */}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </>
  );
};

export default EditPaymentVoucher;
