import React from 'react';
import { FaChevronLeft, FaChevronRight, FaPaperclip, FaPencilAlt, FaTrash } from 'react-icons/fa';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import CustomButton from '../../../Components/CustomButton';

const TmnDealView = ({
  data,
  searchType,
  onEdit,
  onDelete,
  onAttachment,
  showAllocationTable,
  allocationData,
  allocationTableHeaders
}) => {
  const handleAllocationAction = (type, item) => {
    console.log(`${type} allocation:`, item);
  };

  const renderAllocationTable = () => {
    if (!allocationData?.length) return null;

    return (
      <CustomTable
        hasFilters={false}
        setFilters={false}
        headers={allocationTableHeaders}
        pagination={{
          total: allocationData.length,
          per_page: 10,
          current_page: 1
        }}
        isLoading={false}
        sortKey={false}
        sortOrder={false}
        handleSort={false}
        isPaginated={false}
      >
        <tbody>
          {allocationData.map((row) => (
            <tr key={row.id}>
              <td>{row.id}</td>
              <td>{row.accountName}</td>
              <td>{row.amount}</td>
              <td>{row.docType}</td>
              <td>{row.number}</td>
              <td>{row.bank}</td>
              <td>{row.code}</td>
              <td>{row.city}</td>
              <td>{row.description}</td>
              <td>
                <div className="d-flex gap-2">
                  <FaPencilAlt
                    style={{ cursor: 'pointer' }}
                    onClick={() => handleAllocationAction('edit', row)}
                  />
                  <FaTrash
                    style={{ cursor: 'pointer' }}
                    onClick={() => handleAllocationAction('delete', row)}
                  />
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </CustomTable>
    );
  };

  if (!data) return null;

  return (
    <>
      <div style={{ maxWidth: 780 }}>
        <div className="row">
          <div className="col-md-6">
            <div className="mb-4">
              <label className="text-muted mb-2">Type</label>
              <div>{data.type}</div>
            </div>

            <div className="mb-4">
              <label className="text-muted mb-2">Account</label>
              <div className="d-flex gap-2">
                <div>{data.account.type}</div>
                <div>{data.account.name}</div>
              </div>
            </div>

            <div className="mb-4">
              <label className="text-muted mb-2">Bank</label>
              <div>{data.bank}</div>
            </div>

            <div className="mb-4">
              <label className="text-muted mb-2">City</label>
              <div>{data.city}</div>
            </div>

            <div className="mb-4">
              <label className="text-muted mb-2">Sell FCy</label>
              <div className="d-flex gap-2">
                <div>{data.sellFCy}</div>
                <div>{data.sellFCAmount}</div>
              </div>
            </div>
          </div>

          <div className="col-md-6">
            <div className="mb-4">
              <label className="text-muted mb-2">Mode</label>
              <div>{data.mode}</div>
            </div>

            <div className="mb-4">
              <label className="text-muted mb-2">Beneficiary</label>
              <div>{data.beneficiary}</div>
            </div>

            <div className="mb-4">
              <label className="text-muted mb-2">Bank Account</label>
              <div>{data.bankAccount}</div>
            </div>

            <div className="mb-4">
              <label className="text-muted mb-2">Purpose</label>
              <div>{data.purpose}</div>
            </div>

            <div className="mb-4">
              <label className="text-muted mb-2">Rate Type</label>
              <div className="d-flex gap-2">
                <div>{data.rateType.type}</div>
                <div>{data.rateType.rate}</div>
              </div>
            </div>
          </div>

          <div className="col-md-6">
            <div className="mb-4">
              <label className="text-muted mb-2">AG FCy</label>
              <div className="d-flex gap-2">
                <div>{data.agFCy.currency}</div>
                <div>{data.agFCy.amount}</div>
              </div>
            </div>
          </div>

          <div className="col-md-6">
            <div className="mb-4">
              <label className="text-muted mb-2">Total</label>
              <div className="d-flex gap-2">
                <div>{data.total.currency}</div>
                <div>{data.total.amount}</div>
              </div>
            </div>
          </div>

          <div className="col-12">
            <div className="mb-4">
              <div>{data.receivableComm}</div>
            </div>
          </div>
        </div>

        <div className="mt-3">
          <div className="d-flex gap-3 flex-wrap">
            <CustomButton text="Edit" onClick={onEdit} />
            <CustomButton text="Delete" onClick={() => onDelete(data)} className={'secondaryButton'} />
            <CustomButton text="Print" className={'secondaryButton'} />
            <div className="ms-auto d-flex gap-2">
              <FaChevronLeft size={20} style={{ cursor: 'pointer' }} />
              <FaChevronRight size={20} style={{ cursor: 'pointer' }} />
              <FaPaperclip size={20} style={{ cursor: 'pointer' }} onClick={onAttachment} />
            </div>
          </div>
          <div className="mt-3">
            <div>Last {searchType === 'buy' ? 'TBN' : 'TSN'} No: {data.id}</div>
          </div>
        </div>
      </div>
      {showAllocationTable && renderAllocationTable()}
    </>
  );
};

export default TmnDealView;