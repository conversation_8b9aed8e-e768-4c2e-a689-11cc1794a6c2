import React, { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import CustomButton from '../../../Components/CustomButton';
import CustomModal from '../../../Components/CustomModal';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import {
  getAccountToAccountListing,
  deleteAccountToAccount,
  getAccountToAccountAttachments,
  addAccountToAccountAttachment,
  deleteAccountToAccountAttachment,
  getAccountBalance,
} from '../../../Services/Transaction/AccountToAccount';
import { showToast } from '../../../Components/Toast/Toast';
import { useNavigate } from 'react-router-dom';
import { formatDate, isNullOrEmpty } from '../../../Utils/Utils';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';

const ViewAccountToAccount = ({
  searchTerm,
  setSearchTerm,
  setPageState,
  lastVoucherNumbers,
}) => {
  const queryClient = useQueryClient();
  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Fetch main A2A data
  const {
    data: { data: [accountToAccountData] = [] } = {},
    isLoading,
    isError,
    isFetching,
  } = useQuery({
    queryKey: ['accountToAccount', searchTerm],
    queryFn: () => getAccountToAccountListing({ search: searchTerm }),
  });
  // Helper function to map ledger to account type
  const mapLedgerToAccountType = (ledger) => {
    switch (ledger?.toLowerCase()) {
      case 'gl':
        return 'general';
      case 'pl':
        return 'party';
      case 'wic':
        return 'walkin';
      default:
        return ledger?.toLowerCase() || 'general';
    }
  };

  // Fetch account balances for debit and credit accounts
  const { data: debitAccountBalance, error: debitBalanceError } = useQuery({
    queryKey: [
      'accountBalance',
      accountToAccountData?.debit_account_details?.id,
    ],
    queryFn: () =>
      getAccountBalance(
        accountToAccountData?.debit_account_details?.id,
        mapLedgerToAccountType(accountToAccountData?.debit_ledger)
      ),
    enabled:
      !!accountToAccountData?.debit_account_details?.id &&
      !!accountToAccountData?.debit_ledger,
  });
  const { data: creditAccountBalance, error: creditBalanceError } = useQuery({
    queryKey: [
      'accountBalance',
      accountToAccountData?.credit_account_details?.id,
    ],
    queryFn: () =>
      getAccountBalance(
        accountToAccountData?.credit_account_details?.id, mapLedgerToAccountType(accountToAccountData?.credit_ledger)
      ),
    enabled:
      !!accountToAccountData?.credit_account_details?.id &&
      !!accountToAccountData?.credit_ledger,
  });
  if (debitBalanceError) {
    console.error('Debit Balance Error:', debitBalanceError);
  }
  if (creditBalanceError) {
    console.error('Credit Balance Error:', creditBalanceError);
  }

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: (id) => deleteAccountToAccount(id),
    onSuccess: () => {
      showToast('Voucher deleted successfully!', 'success');
      queryClient.invalidateQueries(['accountToAccount', searchTerm]);
      setShowDeleteModal(false);
      setPageState('new');
      setSearchTerm('');
    },
    onError: (error) => {
      setShowDeleteModal(false);
      showToast(error.message || 'Error deleting Voucher', 'error');
    },
  });

  if (isLoading) {
    return (
      <>
        <div className="d-card mt-3">
          <div className="row">
            {/* Left: Details */}
            <div className="col-xxl-9 col-12">
              <div style={{ maxWidth: 780 }}>
                <div className="row">
                  {Array.from({ length: 8 }).map((_, i) => (
                    <div key={i} className="col-md-6 mb-4">
                      <div className="mb-2">
                        <Skeleton
                          duration={1}
                          width={'40%'}
                          baseColor="#ddd"
                          height={16}
                        />
                      </div>
                      <div>
                        <Skeleton
                          duration={1}
                          width={'80%'}
                          baseColor="#ddd"
                          height={20}
                        />
                      </div>
                    </div>
                  ))}
                  <div className="col-12 mb-4">
                    <div className="mb-2">
                      <Skeleton
                        duration={1}
                        width={'20%'}
                        baseColor="#ddd"
                        height={16}
                      />
                    </div>
                    <div>
                      <Skeleton
                        duration={1}
                        width={'100%'}
                        baseColor="#ddd"
                        height={20}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Right: Account Balance Skeletons */}
            {/* <div className="col-xxl-3 col-12">
              {Array.from({ length: 2 }).map((_, i) => (
                <div key={i} className="mb-4">
                  <Skeleton
                    duration={1}
                    width={'60%'}
                    baseColor="#ddd"
                    height={20}
                    className="mb-2"
                  />
                  <div className="d-card account-balance-card">
                    <Skeleton
                      duration={1}
                      width={'80%'}
                      baseColor="#ddd"
                      height={16}
                      className="mb-3"
                    />
                    <div className="mb-2">
                      <Skeleton
                        duration={1}
                        width={'100%'}
                        baseColor="#ddd"
                        height={30}
                      />
                    </div>
                    {Array.from({ length: 3 }).map((_, j) => (
                      <Skeleton
                        key={j}
                        duration={1}
                        width={'20%'}
                        baseColor="#ddd"
                        height={25}
                        className="mb-1"
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div> */}
          </div>
        </div>
      </>
    );
  }
  if (isError || !accountToAccountData) {
    return (
      <div className="d-card">
        <p className="text-danger mb-0">
          Unable to fetch Account to Account data
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="d-card mt-3">
        <div className="row">
          {/* Left: Details */}
          <div className="col-xxl-9 col-12">
            <div style={{ maxWidth: 780 }}>
              <div className="row">
                <div className="col-md-6">
                  <div className="mb-4">
                    <label className="text-muted mb-2">Debit Account</label>
                    <div className="d-flex gap-2">
                      <div>
                        {
                          accountToAccountData?.account_to_account
                            ?.debit_account_details?.title
                        }
                      </div>
                    </div>
                  </div>
                  <div className="mb-4">
                    <label className="text-muted mb-2">Cheque Number</label>
                    <div className="d-flex gap-2">
                      <div>
                        {
                          accountToAccountData?.account_to_account?.cheque
                            ?.cheque_number
                        }
                      </div>
                    </div>
                  </div>
                  <div className="mb-4">
                    <label className="text-muted mb-2">Currency</label>
                    <div>
                      {
                        accountToAccountData?.account_to_account?.currency
                          ?.currency_code
                      }
                    </div>
                  </div>
                  <div className="mb-4">
                    <label className="text-muted mb-2">
                      Debit Account Narration
                    </label>
                    <div>
                      {
                        accountToAccountData?.account_to_account
                          ?.debit_account_narration
                      }
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="mb-4">
                    <label className="text-muted mb-2">Credit Account</label>
                    <div className="d-flex gap-2">
                      <div>
                        {
                          accountToAccountData?.account_to_account
                            ?.credit_account_details?.title
                        }
                      </div>
                    </div>
                  </div>
                  <div className="mb-4">
                    <label className="text-muted mb-2">Accont Title</label>
                    <div>
                      {accountToAccountData?.account_to_account?.account_title}
                    </div>
                  </div>
                  <div className="mb-4">
                    <label className="text-muted mb-2">FC Amount</label>
                    <div>
                      {accountToAccountData?.account_to_account?.fc_amount}
                    </div>
                  </div>
                  <div className="mb-4">
                    <label className="text-muted mb-2">
                      Credit Account Narration
                    </label>
                    <div>
                      {
                        accountToAccountData?.account_to_account
                          ?.credit_account_narration
                      }
                    </div>
                  </div>
                </div>
                <div className="col-12">
                  <div className="mb-4">
                    <label className="text-muted mb-2">Comment</label>
                    <div>
                      {accountToAccountData?.account_to_account?.comment}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Right: Account Balances */}
          {/* <div className="col-xxl-3 col-12">
            <AccountBalanceCard
              heading="Debit Account Balance"
              accountName={
                accountToAccountData?.debit_account_details?.title ||
                'Debit Account'
              }
              balances={debitAccountBalance?.balances || []}
              loading={debitAccountBalance === undefined}
            />
            <AccountBalanceCard
              heading="Credit Account Balance"
              accountName={
                accountToAccountData?.credit_account_details?.title ||
                'Credit Account'
              }
              balances={creditAccountBalance?.balances || []}
              loading={creditAccountBalance === undefined}
            />
          </div> */}
        </div>
      </div>
      <VoucherNavigationBar
        isDisabled={isLoading || isError || isNullOrEmpty(accountToAccountData)}
        actionButtons={[
          { text: 'Edit', onClick: () => setPageState('edit') },
          {
            text: 'Delete',
            onClick: () => setShowDeleteModal(true),
            variant: 'secondaryButton',
          },
        ]}
        loading={isLoading || isFetching}
        onAttachmentClick={() => setShowAttachmentsModal(true)}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />
      {/* Attachments Modal */}
      <CustomModal
        show={showAttachmentsModal}
        close={() => setShowAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          showModal={showAttachmentsModal}
          closeModal={() => setShowAttachmentsModal(false)}
          item={accountToAccountData}
          deleteService={deleteAccountToAccountAttachment}
          uploadService={addAccountToAccountAttachment}
          getAttachmentsService={getAccountToAccountAttachments}
          closeUploader={() => setShowAttachmentsModal(false)}
          voucherAttachment={true}
          viewOnly
          queryToInvalidate={['accountToAccount', searchTerm]}
        />
      </CustomModal>
      {/* Delete Modal */}
      <CustomModal
        show={showDeleteModal}
        close={() => setShowDeleteModal(false)}
        action={() => {
          if (accountToAccountData) {
            deleteMutation.mutate(accountToAccountData?.voucher_no);
          }
        }}
        title="Delete"
        description={`Are you sure you want to delete A2A Number ${accountToAccountData?.voucher?.voucher_no}?`}
        disableClick={deleteMutation.isPending}
      />
    </>
  );
};

export default ViewAccountToAccount;
