import React, { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import BackButton from '../../../../Components/BackButton';
import BeneficiaryRegisterForm from '../../../../Components/BeneficiaryRegisterForm/BeneficiaryRegisterForm';
import ChartOfAccountForm from '../../../../Components/ChartOfAccountForm/ChartOfAccountForm';
import CustomButton from '../../../../Components/CustomButton';
import CustomModal from '../../../../Components/CustomModal';
import PartyLedgerForm from '../../../../Components/PartyLedgerForm/PartyLedgerForm';
import WalkInCustomerForm from '../../../../Components/WalkInCustomerForm/WalkInCustomerForm';
import { usePageTitle } from '../../../../Hooks/usePageTitle';
import '../../transactionStyles.css';
import NewInwardPaymentPay from './NewInwardPaymentPay';
import { useParams } from 'react-router-dom';

const InwardPaymentPay = () => {
  usePageTitle('Payment Voucher');
  const { id } = useParams();
  // [new, view, edit,  listing]
  // View is for specific Voucher search and view it's detail
  // Edit is for editing the specific Voucher's detail
  // Listing is for Voucher listing table
  const [pageState, setPageState] = useState('new');

  // const [isDisabled, setIsDisabled] = useState(true);
  const [showAddLedgerModal, setShowAddLedgerModal] = useState('');
  const [newlyCreatedAccount, setNewlyCreatedAccount] = useState(null);
  // Upload Only Modal
  const [uploadAttachmentsModal, setUploadAttachmentsModal] = useState(false);
  // Upload And View Modal
  // const [attachmentsModal, setAttachmentsModal] = useState(false);
  // Selected files from UploadAttachments Modal
  const [selectedFiles, setSelectedFiles] = useState(null);

  const renderAddLedgerForm = () => {
    switch (showAddLedgerModal) {
      case 'add new pl':
        return (
          <PartyLedgerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new wic':
        return (
          <WalkInCustomerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new gl':
        return (
          <ChartOfAccountForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new beneficiary':
        return (
          <BeneficiaryRegisterForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      default:
        break;
    }
  };

  return (
    <>
      <section className="position-relative">
        <div
          style={{ height: 43 }}
          className="d-flex gap-3 justify-content-between align-items-center flex-wrap mb-4"
        >
          <div>
            {pageState == 'listing' ||
              (pageState == 'view' && (
                <BackButton
                  handleBack={() => {
                    setPageState('new');
                  }}
                />
              ))}
            <h2 className="screen-title mb-0">Payment Voucher</h2>
          </div>
        </div>
        <Row>
          <Col xs={12}>
            <NewInwardPaymentPay
              setShowAddLedgerModal={setShowAddLedgerModal}
              newlyCreatedAccount={newlyCreatedAccount}
              uploadAttachmentsModal={uploadAttachmentsModal}
              setUploadAttachmentsModal={setUploadAttachmentsModal}
              selectedFiles={selectedFiles}
              setSelectedFiles={setSelectedFiles}
            />
          </Col>
        </Row>
      </section>

      {/* Add New Ledger Modal */}
      <CustomModal
        show={!!showAddLedgerModal}
        close={() => setShowAddLedgerModal('')}
        size="xl"
        style={{ minHeight: '812px' }}
      >
        {renderAddLedgerForm()}
      </CustomModal>
    </>
  );
};

export default InwardPaymentPay;
