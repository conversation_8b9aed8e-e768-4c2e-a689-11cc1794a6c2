import React, { useRef, useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Formik, Form, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomModal from '../../../Components/CustomModal';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import CombinedInputs from '../../../Components/CombinedInputs/CombinedInputs';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import {
  getAccountToAccountListing,
  updateAccountToAccount,
  addAccountToAccountAttachment,
  getAccountToAccountAttachments,
  deleteAccountToAccountAttachment,
  getCurrencies,
  getBanks,
  getChequeNumbersByBank,
  getAccountBalance,
  getExchangeRates,
} from '../../../Services/Transaction/AccountToAccount';
import { showToast } from '../../../Components/Toast/Toast';
import useSettingsStore from '../../../Stores/SettingsStore';
import useFormStore from '../../../Stores/FormStore';
import useAccountsByType from '../../../Hooks/useAccountsByType';
import { useNavigate } from 'react-router-dom';
import { accountToAccountvalidationSchema } from '../../../Utils/Validations/ValidationSchemas';



const EditAccountToAccount = ({
  date,
  setPageState,
  setShowAddLedgerModal,
  lastVoucherNumbers,
  setSearchTerm,
  searchTerm,
  isDisabled = false,
  setIsDisabled,
  newlyCreatedAccount,
}) => {
  const formikRef = useRef();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  // Form store for Special Commission navigation
  const {
    saveFormValues,
    getFormValues,
    hasFormValues,
    setLastVisitedPage,
    getLastVisitedPage,
    clearLastVisitedPage,
  } = useFormStore();
  const formId = 'edit-account-to-account';

  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);
  const [addedAttachments, setAddedAttachments] = useState([]);
  const [selectedBank, setSelectedBank] = useState('');
  const [currencyOptions, setCurrencyOptions] = useState([]);
  const [chequeOptions, setChequeOptions] = useState([]);
  const [showSearchTable, setShowSearchTable] = useState(false);
  const [showSearchResult, setShowSearchResult] = useState(false);
  const [selectedDebitAccount, setSelectedDebitAccount] = useState(null);
  const [selectedCreditAccount, setSelectedCreditAccount] = useState(null);

  // Special Commission state
  const [specialCommissionValues, setSpecialCommissionValues] = useState({
    ledger: null,
    account: null,
    amount: '',
    currency: null,
    commission_type: null,
    date: date,
    current: lastVoucherNumbers?.current,
  });
  const [addedSpecialCommissionValues, setAddedSpecialCommissionValues] =
    useState(null);

  // Print settings
  const { getPrintSettings, updatePrintSetting } = useSettingsStore();

  // Get account options using custom hook
  const { getAccountsByTypeOptions } = useAccountsByType({
    includeBeneficiary: false, // No beneficiary accounts needed for Account to Account
    staleTime: 1000 * 60 * 5, // 5 minutes cache
  });

  // Load saved form if present (for Special Commission navigation)
  useEffect(() => {
    const lastPage = getLastVisitedPage(formId);
    if (
      lastPage === 'special-commission' &&
      hasFormValues(formId) &&
      formikRef.current
    ) {
      const savedValues = getFormValues(formId);

      // Restore form values
      formikRef.current.setValues(savedValues);
      setIsDisabled(false);

      if (hasFormValues('special-commission')) {
        setAddedSpecialCommissionValues(getFormValues('special-commission'));
      }
      // Clear lastVisitedPage so it doesn't persist beyond one use
      clearLastVisitedPage(formId);
    }
  }, []);

  // Update special commission values when form data changes
  useEffect(() => {
    if (lastVoucherNumbers?.current) {
      setSpecialCommissionValues((prev) => ({
        ...prev,
        current: lastVoucherNumbers?.current,
      }));
    }
    if (date) {
      setSpecialCommissionValues((prev) => ({
        ...prev,
        date: date,
      }));
    }
  }, [lastVoucherNumbers?.current, date]);

  // Fetch currencies based on selected bank
  const { data: currenciesData } = useQuery({
    queryKey: ['currencies', selectedBank],
    queryFn: () => getCurrencies(selectedBank),
    enabled: !!selectedBank, // Only fetch when bank is selected
  });

  // Fetch cheque numbers based on selected bank
  const { data: chequeNumbersData } = useQuery({
    queryKey: ['chequeNumbers', selectedBank],
    queryFn: () => getChequeNumbersByBank(selectedBank),
    enabled: !!selectedBank,
  });

  // Fetch exchange rates
  const { data: exchangeRatesData } = useQuery({
    queryKey: ['exchangeRates'],
    queryFn: getExchangeRates,
    staleTime: 1000 * 60 * 5, // 5 minutes cache
  });

  // Fetch account balance for debit account
  const { data: debitAccountBalance } = useQuery({
    queryKey: [
      'accountBalance',
      selectedDebitAccount?.value,
      selectedDebitAccount?.accountType,
    ],
    queryFn: () =>
      getAccountBalance(
        selectedDebitAccount.value,
        selectedDebitAccount.accountType
      ),
    enabled:
      !!selectedDebitAccount?.value && !!selectedDebitAccount?.accountType,
    staleTime: 1000 * 60 * 2, // 2 minutes cache
  });

  // Fetch account balance for credit account
  const { data: creditAccountBalance } = useQuery({
    queryKey: [
      'accountBalance',
      selectedCreditAccount?.value,
      selectedCreditAccount?.accountType,
    ],
    queryFn: () =>
      getAccountBalance(
        selectedCreditAccount.value,
        selectedCreditAccount.accountType
      ),
    enabled:
      !!selectedCreditAccount?.value && !!selectedCreditAccount?.accountType,
    staleTime: 1000 * 60 * 2, // 2 minutes cache
  });



  useEffect(() => {
    if (currenciesData) {
      setCurrencyOptions(
        currenciesData.map((currency) => ({
          label: currency.name,
          value: currency.id,
        }))
      );
    } else if (!selectedBank) {
      // Provide default currency options when no bank is selected
      setCurrencyOptions([
        { label: 'DHS', value: 'dhs' },
        { label: 'USD', value: 'usd' },
        { label: 'EUR', value: 'eur' },
      ]);
    }
  }, [currenciesData, selectedBank]);

  useEffect(() => {
    if (chequeNumbersData) {
      setChequeOptions(
        chequeNumbersData.map((cheque) => ({
          label: cheque.number,
          value: cheque.id,
        }))
      );
    } else if (!selectedBank) {
      // Provide default cheque options when no bank is selected
      setChequeOptions([
        { label: 'Select Bank First', value: null, isDisabled: true },
      ]);
    }
  }, [chequeNumbersData, selectedBank]);

  // Effect to determine bank from selected accounts and update selectedBank
  useEffect(() => {
    // Logic to determine bank from debit or credit account
    // This assumes that bank accounts have a specific identifier or type
    // You may need to adjust this logic based on your API response structure
    let bankId = null;

    if (
      selectedDebitAccount?.accountType === 'general' &&
      selectedDebitAccount?.bankId
    ) {
      bankId = selectedDebitAccount.bankId;
    } else if (
      selectedCreditAccount?.accountType === 'general' &&
      selectedCreditAccount?.bankId
    ) {
      bankId = selectedCreditAccount.bankId;
    }

    if (bankId && bankId !== selectedBank) {
      setSelectedBank(bankId);
    }
  }, [selectedDebitAccount, selectedCreditAccount, selectedBank]);

  // Fetch the voucher details
  const {
    data: { data: [accountToAccountData] = [] } = {},
    isLoading,
    isError,
  } = useQuery({
    queryKey: ['accountToAccount', searchTerm],
    queryFn: () => getAccountToAccountListing({ search: searchTerm }),
    enabled: !!searchTerm,
  });
 
  // Prepare initial values from fetched data
  const initialValues = accountToAccountData
    ? {
        debitLedger: accountToAccountData.debit_ledger || '',
        debitAccount: accountToAccountData.debit_account || '',
        creditLedger: accountToAccountData.credit_ledger || '',
        creditAccount: accountToAccountData.credit_account || '',
        accountTitle: accountToAccountData.account_title || '',
        accountTitleDisplay:
          accountToAccountData.account_title_display || 'show',
        chequeNumber: accountToAccountData.cheque_number || '',
        currency: accountToAccountData.currency || '',
        fcAmount: accountToAccountData.fc_amount || '',
        debitNarration: accountToAccountData.debit_narration || '',
        creditNarration: accountToAccountData.credit_narration || '',
        comment: accountToAccountData.comment || '',
      }
    : {
        debitLedger: '',
        debitAccount: '',
        creditLedger: '',
        creditAccount: '',
        accountTitle: '',
        accountTitleDisplay: 'show',
        chequeNumber: '',
        currency: '',
        fcAmount: '',
        debitNarration: '',
        creditNarration: '',
        comment: '',
      };

  const updateMutation = useMutation({
    mutationFn: (payload) =>
      updateAccountToAccount(accountToAccountData.id, payload),
    onSuccess: (data) => {
      showToast('Account to Account Updated!', 'success');
      // Handle print functionality
      if (getPrintSettings('account_to_account')) {
        if (data?.detail?.pdf_url) {
          window.open(data.detail.pdf_url, '_blank');
        }
      }
      // Invalidate and refetch queries following Receipt Voucher pattern
      queryClient.invalidateQueries(['accountToAccount', searchTerm]);
      queryClient.refetchQueries(['accountToAccount', searchTerm]);
      queryClient.invalidateQueries(['accountToAccountListing']);
      queryClient.refetchQueries(['accountToAccountListing']);

      setPageState('view');
      setIsDisabled(true);
    },
    onError: (error) => {
      showToast(error.message || 'Error updating Account to Account', 'error');
    },
  });

  const handleSubmit = (values) => {
    updateMutation.mutate(values);
  };

  const handleCancel = () => {
    setIsDisabled(true);
    setShowSearchResult(false);
    setShowSearchTable(false);
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
    setAddedAttachments([]);
  };

  // Special Commission navigation function
  const handleNavigateToSpecialCommissionPage = () => {
    // Check if required fields are filled
    const requiredFields = [
      'debitLedger',
      'debitAccount',
      'fcAmount',
      'currency',
    ];
    const missingFields = requiredFields.filter(
      (field) => !formikRef.current.values[field]
    );
    if (missingFields.length > 0) {
      // Set touched for all required fields to show errors
      const touchedFields = {};
      requiredFields.forEach((field) => {
        touchedFields[field] = true;
      });
      formikRef.current.setTouched({
        ...formikRef.current.touched,
        ...touchedFields,
      });
      return;
    }

    // If all required fields are filled, proceed
    if (formikRef.current && !isDisabled) {
      saveFormValues(formId, formikRef.current.values);
      setLastVisitedPage(formId, 'special-commission');
    }

    // Check if we already have special commission data
    const hasExistingCommission = !!addedSpecialCommissionValues;

    setPageState('edit');
    navigate('/transactions/special-commission', {
      state: {
        fromPage: 'account-to-account',
        pageState: 'edit',
        searchTerm: searchTerm,
        values: {
          rvValues: {
            ...specialCommissionValues,
            ledger: formikRef.current.values.debitLedger,
            account: formikRef.current.values.debitAccount,
            amount: formikRef.current.values.fcAmount,
            currency: formikRef.current.values.currency,
            date: date,
            current: lastVoucherNumbers?.current,
          },
          isEdit: hasExistingCommission,
        },
      },
    });
  };

  if (isLoading) {
    return (
      <div className="d-card">
        <p>Loading...</p>
      </div>
    );
  }
  if (isError || !accountToAccountData) {
    return (
      <div className="d-card">
        <p className="text-danger mb-0">
          Unable to fetch Account to Account data
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={initialValues}
          validationSchema={ accountToAccountvalidationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({
            values,
            errors,
            touched,
            handleChange,
            handleBlur,
            setFieldValue,
          }) => {
            // Helper function to handle account loading
            const handleLedgerChange = (
              ledgerType,
              fieldName,
              setAccountField
            ) => {
              setFieldValue(fieldName, ledgerType);
              setFieldValue(setAccountField, ''); // Clear account when ledger changes
            };

            return (
              <Form>
                <div className="row">
                  <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
                    <div className="row">
                      {/* Debit Account Section */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="Debit Account"
                          type1="select"
                          type2="select"
                          name1="debitLedger"
                          name2="debitAccount"
                          value1={values.debitLedger}
                          value2={
                            values.debitAccount || newlyCreatedAccount?.id
                          }
                          options1={[
                            { label: 'PL', value: 'party' },
                            { label: 'GL', value: 'general' },
                            { label: 'WIC', value: 'walkin' },
                          ]}
                          options2={getAccountsByTypeOptions(
                            values.debitLedger
                          )}
                          isDisabled={isDisabled}
                          handleBlur={handleBlur}
                          placeholder1="Ledger"
                          placeholder2="Select Account"
                          className1="ledger"
                          className2="account"
                          onChange1={(selected) => {
                            handleLedgerChange(
                              selected.value,
                              'debitLedger',
                              'debitAccount'
                            );
                          }}
                          onChange2={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                            } else {
                              setFieldValue('debitAccount', selected.value);
                              // Track selected debit account for balance fetching
                              setSelectedDebitAccount({
                                value: selected.value,
                                label: selected.label,
                                accountType: values.debitLedger,
                              });
                            }
                          }}
                        />
                      </div>

                      {/* Credit Account Section */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="Credit Account"
                          type1="select"
                          type2="select"
                          name1="creditLedger"
                          name2="creditAccount"
                          value1={values.creditLedger}
                          value2={
                            values.creditAccount || newlyCreatedAccount?.id
                          }
                          options1={[
                            { label: 'PL', value: 'party' },
                            { label: 'GL', value: 'general' },
                            { label: 'WIC', value: 'walkin' },
                          ]}
                          options2={getAccountsByTypeOptions(
                            values.creditLedger
                          )}
                          isDisabled={isDisabled}
                          handleBlur={handleBlur}
                          placeholder1="Ledger"
                          placeholder2="Select Account"
                          className1="ledger"
                          className2="account"
                          onChange1={(selected) => {
                            handleLedgerChange(
                              selected.value,
                              'creditLedger',
                              'creditAccount'
                            );
                          }}
                          onChange2={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                            } else {
                              setFieldValue('creditAccount', selected.value);
                              // Track selected credit account for balance fetching
                              setSelectedCreditAccount({
                                value: selected.value,
                                label: selected.label,
                                accountType: values.creditLedger,
                              });
                            }
                          }}
                        />
                        {/* Switch Account Button */}
                        <div className="d-flex justify-content-end mt-2">
                          <CustomButton
                            text="Switch Account"
                            type="button"
                            variant="secondaryButton"
                            size="sm"
                            disabled={isDisabled || (!values.debitAccount && !values.creditAccount)}
                            onClick={() => {
                              // Switch debit and credit accounts
                              const tempDebitLedger = values.debitLedger || '';
                              const tempDebitAccount = values.debitAccount || '';
                              const tempCreditLedger = values.creditLedger || '';
                              const tempCreditAccount = values.creditAccount || '';
                              const tempSelectedDebit = selectedDebitAccount;
                              const tempSelectedCredit = selectedCreditAccount;

                              // Update form values
                              setFieldValue('debitLedger', tempCreditLedger);
                              setFieldValue('debitAccount', tempCreditAccount);
                              setFieldValue('creditLedger', tempDebitLedger);
                              setFieldValue('creditAccount', tempDebitAccount);

                              // Update selected account states
                              setSelectedDebitAccount(tempSelectedCredit);
                              setSelectedCreditAccount(tempSelectedDebit);

                              console.log('Switched accounts:', {
                                from: { debit: tempDebitAccount, credit: tempCreditAccount },
                                to: { debit: tempCreditAccount, credit: tempDebitAccount }
                              });
                            }}
                          />
                        </div>
                      </div>

                      {/* Cheque Number */}
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name="chequeNumber"
                          label="Cheque Number"
                          options={chequeOptions}
                          value={values.chequeNumber}
                          onChange={(selected) =>
                            setFieldValue('chequeNumber', selected.value)
                          }
                          onBlur={handleBlur}
                          placeholder="Select Cheque Number"
                          isDisabled={isDisabled}
                        />
                        <ErrorMessage
                          name="chequeNumber"
                          component="div"
                          className="input-error-message text-danger"
                        />
                      </div>

                      {/* Account Title Show/Hide */}
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name="accountTitleDisplay"
                          label="Account Title Display"
                          options={[
                            { label: 'Show', value: 'show' },
                            { label: 'Hide', value: 'hide' },
                          ]}
                          value={values.accountTitleDisplay}
                          onChange={(selected) =>
                            setFieldValue('accountTitleDisplay', selected.value)
                          }
                          onBlur={handleBlur}
                          placeholder="Show"
                          isDisabled={isDisabled}
                        />
                      </div>

                      {/* Account Title */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CustomInput
                          name="accountTitle"
                          label="Account Title"
                          value={values.accountTitle}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter Account Title"
                          disabled={isDisabled}
                          error={touched.accountTitle && errors.accountTitle}
                        />
                      </div>

                      {/* Currency and FC Amount */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="Currency"
                          type1="select"
                          type2="input"
                          name1="currency"
                          name2="fcAmount"
                          value1={values.currency}
                          value2={values.fcAmount}
                          options1={currencyOptions}
                          isDisabled={isDisabled}
                          handleBlur={handleBlur}
                          placeholder1="DHS"
                          placeholder2="Enter FC Amount"
                          inputType2="number"
                          className1="currency"
                          className2="amount"
                          onChange1={(selected) => {
                            setFieldValue('currency', selected.value);
                            // Set bank for cheque number fetching if currency has bank info
                            if (selected.bank_id) {
                              setSelectedBank(selected.bank_id);
                            }
                          }}
                          onChange2={handleChange}
                        />
                      </div>

                      {/* Debit Account Narration */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CustomInput
                          name="debitNarration"
                          label="Debit Account Narration"
                          type="textarea"
                          rows={3}
                          value={values.debitNarration}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter Debit Account Narration"
                          disabled={isDisabled}
                          error={
                            touched.debitNarration && errors.debitNarration
                          }
                        />
                      </div>

                      {/* Credit Account Narration */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CustomInput
                          name="creditNarration"
                          label="Credit Account Narration"
                          type="textarea"
                          rows={3}
                          value={values.creditNarration}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter Credit Account Narration"
                          disabled={isDisabled}
                          error={
                            touched.creditNarration && errors.creditNarration
                          }
                        />
                      </div>

                      {/* Comment */}
                      <div className="col-12 mb-45">
                        <CustomInput
                          name="comment"
                          label="Comment"
                          type="textarea"
                          rows={4}
                          value={values.comment}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter Comment"
                          disabled={isDisabled}
                          error={touched.comment && errors.comment}
                        />
                      </div>
                    </div>

                    {/* Add Special Commission Button */}
                    <div className="d-flex mb-5">
                      <CustomButton
                        type="button"
                        onClick={handleNavigateToSpecialCommissionPage}
                        text={`${
                          !!addedSpecialCommissionValues ? 'Edit' : 'Add'
                        } Special Commission`}
                        disabled={isDisabled}
                      />
                    </div>
                    {!!addedSpecialCommissionValues ? (
                      <p
                        style={{
                          color: '#22C55E',
                          fontSize: '14px',
                          marginBottom: '20px',
                        }}
                      >
                        Special Commission has been added successfully.
                      </p>
                    ) : null}

                    {/* Action Buttons */}
                    <div className="d-flex gap-3 mt-3">
                      <CustomButton
                        text="Save"
                        type="submit"
                        disabled={isDisabled || updateMutation.isPending}
                      />
                      <CustomButton
                        text="Cancel"
                        type="button"
                        variant="secondaryButton"
                        onClick={() => setPageState('view')}
                      />
                    </div>

                    {/* Checkboxes */}
                    <div className="d-inline-block mt-3">
                      <CustomCheckbox
                        label="Account Balance"
                        disabled={isDisabled}
                        style={{ border: 'none', margin: 0 }}
                        onChange={() => {}}
                      />
                      <CustomCheckbox
                        label="Print"
                        checked={getPrintSettings('account_to_account')}
                        onChange={(e) => {
                          updatePrintSetting(
                            'account_to_account',
                            e.target.checked
                          );
                        }}
                        style={{ border: 'none', margin: 0 }}
                        readOnly={isDisabled}
                      />
                    </div>
                  </div>

                  {/* Show balance cards only when !isDisabled */}
                  {!isDisabled && (
                    <div className="col-xxl-3 col-12">
                      {/* Account Balance Cards */}
                      <div>
                        {/* Debit Account Balance */}
                        {selectedDebitAccount && (
                          <div>
                            <h6 className="mb-2">Debit Account Balance</h6>
                            <div className="d-card mb-4 account-balance-card">
                              <div className="mb-3 account-name w-100">
                                {selectedDebitAccount.label}
                              </div>
                              <table className="w-100">
                                <thead>
                                  <tr
                                    style={{
                                      borderBottom: '1px solid #E5E7EB',
                                    }}
                                  >
                                    <th
                                      style={{
                                        padding: '8px 0',
                                        color: '#6B7280',
                                        fontWeight: '500',
                                      }}
                                    >
                                      FCy
                                    </th>
                                    <th
                                      style={{
                                        padding: '8px 0',
                                        color: '#6B7280',
                                        fontWeight: '500',
                                      }}
                                    >
                                      Balance
                                    </th>
                                    <th
                                      style={{
                                        padding: '8px 0',
                                        color: '#6B7280',
                                        fontWeight: '500',
                                      }}
                                    ></th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {debitAccountBalance?.balances?.length > 0 ? (
                                    debitAccountBalance.balances.map(
                                      (balance, index) => (
                                        <tr key={index}>
                                          <td
                                            style={{
                                              padding: '8px 0',
                                              color:
                                                balance.type === 'Dr'
                                                  ? '#EF4444'
                                                  : '#22C55E',
                                              fontWeight: '500',
                                            }}
                                          >
                                            {balance.currency}
                                          </td>
                                          <td style={{ padding: '8px 0' }}>
                                            {balance.amount}
                                          </td>
                                          <td style={{ padding: '8px 0' }}>
                                            {balance.type}
                                          </td>
                                        </tr>
                                      )
                                    )
                                  ) : (
                                    <tr>
                                      <td
                                        colSpan="3"
                                        style={{
                                          padding: '8px 0',
                                          textAlign: 'center',
                                          color: '#6B7280',
                                        }}
                                      >
                                        {debitAccountBalance
                                          ? 'No balance data available'
                                          : 'Loading...'}
                                      </td>
                                    </tr>
                                  )}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        )}

                        {/* Credit Account Balance */}
                        {selectedCreditAccount && (
                          <div>
                            <h6 className="mb-2">Credit Account Balance</h6>
                            <div className="d-card mb-4 account-balance-card">
                              <div className="mb-3 account-name w-100">
                                {selectedCreditAccount.label}
                              </div>
                              <table className="w-100">
                                <thead>
                                  <tr
                                    style={{
                                      borderBottom: '1px solid #E5E7EB',
                                    }}
                                  >
                                    <th
                                      style={{
                                        padding: '8px 0',
                                        color: '#6B7280',
                                        fontWeight: '500',
                                      }}
                                    >
                                      FCy
                                    </th>
                                    <th
                                      style={{
                                        padding: '8px 0',
                                        color: '#6B7280',
                                        fontWeight: '500',
                                      }}
                                    >
                                      Balance
                                    </th>
                                    <th
                                      style={{
                                        padding: '8px 0',
                                        color: '#6B7280',
                                        fontWeight: '500',
                                      }}
                                    ></th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {creditAccountBalance?.balances?.length >
                                  0 ? (
                                    creditAccountBalance.balances.map(
                                      (balance, index) => (
                                        <tr key={index}>
                                          <td
                                            style={{
                                              padding: '8px 0',
                                              color:
                                                balance.type === 'Dr'
                                                  ? '#EF4444'
                                                  : '#22C55E',
                                              fontWeight: '500',
                                            }}
                                          >
                                            {balance.currency}
                                          </td>
                                          <td style={{ padding: '8px 0' }}>
                                            {balance.amount}
                                          </td>
                                          <td style={{ padding: '8px 0' }}>
                                            {balance.type}
                                          </td>
                                        </tr>
                                      )
                                    )
                                  ) : (
                                    <tr>
                                      <td
                                        colSpan="3"
                                        style={{
                                          padding: '8px 0',
                                          textAlign: 'center',
                                          color: '#6B7280',
                                        }}
                                      >
                                        {creditAccountBalance
                                          ? 'No balance data available'
                                          : 'Loading...'}
                                      </td>
                                    </tr>
                                  )}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Exchange Rates Card */}
                      <h6 className="mb-2">
                        Live Exchange Rates Against Base Currency
                      </h6>
                      <div className="d-card account-balance-card">
                        <div className="d-flex justify-content-between align-items-center mb-3">
                          <div className="d-flex align-items-center account-name w-100">
                            <span className="me-2" style={{ color: '#6B7280' }}>
                              Inverse
                            </span>
                            <div className="form-check form-switch">
                              <input
                                className="form-check-input"
                                type="checkbox"
                                style={{ cursor: 'pointer' }}
                              />
                            </div>
                          </div>
                        </div>
                        <table className="w-100">
                          <thead>
                            <tr style={{ borderBottom: '1px solid #E5E7EB' }}>
                              <th
                                style={{
                                  padding: '8px 0',
                                  color: '#6B7280',
                                  fontWeight: '500',
                                }}
                              >
                                FCy
                              </th>
                              <th
                                style={{
                                  padding: '8px 0',
                                  color: '#6B7280',
                                  fontWeight: '500',
                                }}
                              >
                                Rates
                              </th>
                              <th
                                style={{
                                  padding: '8px 0',
                                  color: '#6B7280',
                                  fontWeight: '500',
                                }}
                              >
                                Change (24h)
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            {exchangeRatesData?.length > 0 ? (
                              exchangeRatesData.map((rate, index) => (
                                <tr key={index}>
                                  <td style={{ padding: '8px 0' }}>
                                    {rate.currency}
                                  </td>
                                  <td style={{ padding: '8px 0' }}>
                                    {rate.rate}
                                  </td>
                                  <td
                                    style={{
                                      padding: '8px 0',
                                      color: rate.isPositive
                                        ? '#22C55E'
                                        : '#EF4444',
                                    }}
                                  >
                                    {rate.change}
                                  </td>
                                </tr>
                              ))
                            ) : (
                              <tr>
                                <td
                                  colSpan="3"
                                  style={{
                                    padding: '8px 0',
                                    textAlign: 'center',
                                    color: '#6B7280',
                                  }}
                                >
                                  {exchangeRatesData
                                    ? 'No exchange rates available'
                                    : 'Loading...'}
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
      {!showSearchTable && (
        <VoucherNavigationBar
          isDisabled={isDisabled}
          actionButtons={[
            {
              text: 'Update',
              onClick: () => {
                if (formikRef.current) {
                  formikRef.current.handleSubmit();
                }
              },
            },
            {
              text: 'Cancel',
              onClick: handleCancel,
              variant: 'secondaryButton',
            },
          ]}
          loading={updateMutation.isPending}
          onAttachmentClick={() => setShowAttachmentsModal(true)}
          lastVoucherNumbers={lastVoucherNumbers}
          setPageState={setPageState}
          setSearchTerm={setSearchTerm}
        />
      )}

      <CustomModal
        show={showAttachmentsModal}
        close={() => setShowAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          showModal={showAttachmentsModal}
          closeModal={() => setShowAttachmentsModal(false)}
          item={accountToAccountData}
          deleteService={deleteAccountToAccountAttachment}
          uploadService={addAccountToAccountAttachment}
          closeUploader={() => setShowAttachmentsModal(false)}
          voucherAttachment={true}
          queryToInvalidate={['accountToAccount', searchTerm]}
        />
      </CustomModal>
    </>
  );
};

export default EditAccountToAccount;
