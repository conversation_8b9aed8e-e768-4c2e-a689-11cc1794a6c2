import { Form, Formik } from 'formik';
import React, { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { HiMiniLink, HiPrinter } from 'react-icons/hi2';
import CustomButton from '../../../../Components/CustomButton';
import CustomInput from '../../../../Components/CustomInput';
import CustomModal from '../../../../Components/CustomModal';
import CustomTable from '../../../../Components/CustomTable/CustomTable';
import StatusChip from '../../../../Components/StatusChip/StatusChip';
import TableActionDropDown from '../../../../Components/TableActionDropDown/TableActionDropDown';
import withFilters from '../../../../HOC/withFilters ';
import { usePageTitle } from '../../../../Hooks/usePageTitle';
import { MOCK_INWARD_PAYMENT_PAGE_DATA } from '../../../../Mocks/MockData';
import { inwardPaymentHeaders } from '../../../../Utils/Constants/TableHeaders';
import { useNavigate } from 'react-router-dom';

const InwardPayment = ({ filters, setFilters, pagination }) => {
  usePageTitle('Inward Payment');
  const [showHoldingReasonModal, setShowHoldingReasonModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  // Update the table data reference
  const tableData = MOCK_INWARD_PAYMENT_PAGE_DATA;
  const isLoading = false;
  const isError = false;

  const handleHoldingReasonSubmit = (values) => {
    console.log(selectedItem, values);
    setShowHoldingReasonModal(false);
  };

  const navigate = useNavigate();

  return (
    <section>
      <div className="d-flexflex-wrap mb-3">
        <h2 className="screen-title m-0 d-inline">
          Inward Payment
        </h2>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={inwardPaymentHeaders}
            pagination={pagination}
            isLoading={isLoading}
            useApplyButton
            additionalFilters={[
              {
                title: 'FC Amount',
                text: 'number'
              },
            ]}
            selectOptions={[
              {
                title: 'Currency',
                options: [{ value: 'All', label: 'All' }],
              },
              {
                title: 'Account',
                options: [{ value: 'All', label: 'All' }],
              },
              {
                title: '',
                options: [{ value: 'All', label: 'All' }],
              }
            ]}
            dateFilters={[{ title: 'Pay Date' }, {title: 'Debit Note Date'}]}
            rangeFilters={[{ title: 'Debit Note Range' }, {title: 'Amount'}]}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={inwardPaymentHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.payDate}</td>
                    <td>{item.beneficiary}</td>
                    <td>{item.idNumber}</td>
                    <td>{item.sender}</td>
                    <td>{item.contactNo}</td>
                    <td>{item.currency}</td>
                    <td>{item.fcBalanceAmount}</td>
                    <td>{item.fcTotal}</td>
                    <td>{item.refNo}</td>
                    <td>{item.debitNoteNumber}</td>
                    <td>{item.debitNoteDate}</td>
                    <td>{item.debitParty}</td>
                    <td>{item.payType}</td>
                    <td>{item.bank}</td>
                    <td>{item.detail}</td>
                    <td>{item.comment}</td>
                    <td>
                      <div className="d-flex align-items-center">
                        {item?.action.map((action, index) => (
                          <StatusChip
                            key={`${index}-${action}`}
                            status={action}
                            className="ms-2 cp"
                            onClick={() => {
                              console.log(item?.id, action);
                              if (action === 'Hold') {
                                setShowHoldingReasonModal(true);
                                setSelectedItem(item);
                              }
                              else if (action === 'Pay') {
                                navigate(`/transactions/inward-payment/pay/${item?.id}`)
                              }
                            }}
                          />
                        ))}
                        <TableActionDropDown
                          displaySeparator={false}
                          actions={[
                            {
                              name: 'Link',
                              icon: HiMiniLink,
                              onClick: () => console.log(item),
                              className: 'edit',
                            },
                            {
                              name: 'Print',
                              icon: HiPrinter,
                              onClick: () => console.log(item),
                              className: 'attachments',
                            },
                          ]}
                        />
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>

      {/* Holding Reason Modal */}
      <CustomModal
        show={showHoldingReasonModal}
        close={() => setShowHoldingReasonModal(false)}
      >
        <div className="text-center mb-3">
          <h4 className="modalTitle">Holding Reason</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{
              comment: '',
            }}
            // validationSchema={outOfScopeSchema}
            // onSubmit={handleOutOfScopeReasonSubmit}
            onSubmit={handleHoldingReasonSubmit}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    label="Comment"
                    name="comment"
                    required
                    id="comment"
                    type="textarea"
                    rows={1}
                    placeholder="Enter comment"
                    value={values.comment}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.comment && errors.comment}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {/* {!addClassificationMutation.isPending ? ( */}
                  <>
                    <CustomButton type="submit" text={'Save'} />
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={() => setShowHoldingReasonModal(false)}
                    />
                  </>
                  {/* ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} */}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </section>
  );
};
export default withFilters(InwardPayment);
