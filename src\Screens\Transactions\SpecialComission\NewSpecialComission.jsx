import { Form, Formik } from 'formik';
import React, { useCallback, useRef, useState } from 'react';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import { specialComissionHeaders } from '../../../Utils/Constants/TableHeaders';
import SpecialComissionRow from './SpecialComissionRow';

const generateInitialRows = (count) => {
  const rows = {};
  Array.from({ length: count }).forEach(() => {
    const id = crypto.randomUUID();
    rows[id] = {
      id,
      ledger: '',
      credit_account: '',
      narration: '',
      percentage: '',
      amount: '',
    };
  });
  return rows;
};
const INITIAL_STATE = generateInitialRows(5);

const NewSpecialComission = ({
  isDisabled = false,
  setIsDisabled,
  newlyCreatedAccount,
  setShowAddLedgerModal,
}) => {
  const [rows, setRows] = useState(INITIAL_STATE);
  const [totalCommission, setTotalCommission] = useState(0);
  const [difference, setDifference] = useState(0);
  const formikRef = useRef();

  const handleAddRows = () => {
    const newRows = {};
    const id = crypto.randomUUID();
    newRows[id] = {
      id,
      ledger: '',
      credit_account: '',
      narration: '',
      percentage: '',
      amount: '',
    };
    setRows((prevRows) => ({ ...prevRows, ...newRows }));
  };

  const handleSubmit = () => {
    const formValues = formikRef.current.values;
    let payload = {
      ...rows,
      ...formValues,
    };

    console.log('payload', payload);
    // Remove rows that have empty values
    payload = Object.fromEntries(
      Object.entries(payload).filter(([_, obj]) => {
        return Object.entries(obj).every(([_, v]) => {
          return v !== '' && v !== null && v !== undefined;
        });
      })
    );

    console.log('submit payload:', payload);
  };

  const handleCancel = () => {
    setRows(generateInitialRows(3));
    setIsDisabled(true);
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
  };

  // Handler functions for rows
  const updateField = useCallback((id, field, value) => {
    setRows((prev) => {
      const newRows = {
        ...prev,
        [id]: {
          ...prev[id],
          [field]: value,
        },
      };

      return newRows;
    });
  }, []);

  const handleDeleteRow = (id) => {
    setRows((prevRows) => {
      const newState = { ...prevRows };
      delete newState[id];
      return newState;
    });
  };

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={{
            transaction_no: '65238',
            date: '',
            commission_type: '',
            ledger: '',
            account: '',
            currency: '',
            amount: '',
            commission_percentage: '',
            commission_amount: '',
            description: '',
          }}
        >
          {({
            values,
            errors,
            touched,
            handleChange,
            handleBlur,
            setFieldValue,
          }) => (
            <Form>
              <div className="row">
                <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-4">
                      <CustomInput
                        name={'transaction_no'}
                        label={'Transaction No'}
                        type={'text'}
                        defaultValue={values.transaction_no}
                        disabled={true}
                        readOnly
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'date'}
                        label={'Date'}
                        type={'date'}
                        defaultValue={values.date}
                        disabled={true}
                        readOnly
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'commission_type'}
                        label={'Commission Type'}
                        placeholder={'Select Commission Type'}
                        options={[
                          {
                            label: 'Comission Income',
                            value: 'comission_income',
                          },
                          {
                            label: 'Comission Expense',
                            value: 'comission_expense',
                          },
                        ]}
                        value={values.commission_type}
                        onChange={(selected) => {
                          setFieldValue('commission_type', selected.value);
                        }}
                        onBlur={handleBlur}
                      />
                    </div>

                    {/* <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'ledger'}
                        label={'Ledger'}
                        options={[
                          { label: 'PL', value: 'party' },
                          { label: 'GL', value: 'general' },
                          { label: 'WIC', value: 'walkin' },
                        ]}
                        placeholder={'Select Ledger'}
                        value={values.ledger}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div> */}
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'account'}
                        label={'Account'}
                        options={[
                          {
                            label: 'ABC',
                            value: 'abc',
                          },
                          {
                            label: 'DEF',
                            value: 'def',
                          },
                          {
                            label: 'XYZ',
                            value: 'xyz',
                          },
                        ]}
                        placeholder={'Select Account'}
                        value={values.account}
                        onChange={(selected) => {
                          if (
                            selected.label?.toLowerCase()?.startsWith('add new')
                          ) {
                            setShowAddLedgerModal(
                              selected.label?.toLowerCase()
                            );
                          } else {
                            setFieldValue('account', selected.value);
                          }
                        }}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <div className='combined-select-container'>
                        <label htmlFor="currency">Amount</label>
                        <div className='combined-select-input'>
                          <div className='combined-select-left mt-1'>
                            <SearchableSelect
                              name={'currency'}
                              options={[
                                {
                                  label: 'USD',
                                  value: 'usd',
                                },
                                {
                                  label: 'EUR',
                                  value: 'eur',
                                },
                                {
                                  label: 'GBP',
                                  value: 'gbp',
                                },
                              ]}
                              placeholder={'Currency'}
                              value={values.currency}
                              className='ledger-select__control'
                              onChange={(selected) => {
                                setFieldValue('currency', selected.value);
                              }}
                              onBlur={handleBlur}
                            />
                          </div>
                          <div className='separator-between-selects'>|</div>
                          <div className='combined-select-right mt-3'>
                            <CustomInput
                              name={'amount'}
                              inputClass='ledger-select__control'
                              type={'number'}
                              placeholder={'Enter Amount'}
                              value={values.amount}
                              onChange={handleChange}
                              onBlur={handleBlur}
                              error={touched.amount && errors.amount}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'commission_percentage'}
                        label={'Commission %'}
                        type={'number'}
                        min={0}
                        max={100}
                        value={values.commission_percentage}
                        onChange={handleChange}
                        placeholder={"Enter Commission %"}
                        onBlur={handleBlur}
                        error={
                          touched.commission_percentage &&
                          errors.commission_percentage
                        }
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'commission_amount'}
                        label={'Commission Amount (in LC)'}
                        placeholder={"Enter Commission Amount"}
                        type={'number'}
                        value={values.commission_amount}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.commission_amount &&
                          errors.commission_amount
                        }
                      />
                    </div>
                    <div className="col-12 mb-3">
                      <CustomInput
                        name={'description'}
                        label={'Description'}
                        placeholder={"Enter Description"}
                        type={'textarea'}
                        value={values.description}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.description && errors.description}
                      />
                    </div>
                  </div>
                </div>
                <h2 className="screen-title mb-0">Commission Distribution</h2>
                <CustomTable
                  displayCard={false}
                  headers={specialComissionHeaders}
                  isPaginated={false}
                  className={'inputTable'}
                  hideSearch
                  hideItemsPerPage
                >
                  <tbody>
                    {Object.values(rows).map((row, index) => (
                      <SpecialComissionRow
                        key={row.id}
                        row={row}
                        index={index}
                        handleDeleteRow={handleDeleteRow}
                        updateField={updateField}
                        // accountData={accountData}
                        setShowAddLedgerModal={setShowAddLedgerModal}
                      />
                    ))}
                  </tbody>
                </CustomTable>

                <div className="d-flex flex-wrap justify-content-between mt-3 mb-5">
                  <div className="d-flex gap-3 flex-wrap">
                    {[
                      {
                        text: 'Save',
                        onClick: handleSubmit,
                      },
                      {
                        text: 'Add Rows',
                        onClick: handleAddRows,
                        variant: 'secondaryButton',
                      },
                      {
                        text: 'Delete',
                        onClick: () => { },
                        variant: 'secondaryButton',
                      },
                      {
                        text: 'Cancel',
                        onClick: handleCancel,
                        variant: 'secondaryButton',
                      },
                    ].map((button, index) => (
                      <CustomButton
                        key={index}
                        text={button.text}
                        onClick={button.onClick}
                        variant={button?.variant}
                      />
                    ))}
                  </div>
                  <div className="d-flex flex-column gap-2 mt-1 debit-credit-inputs">
                    <CustomInput
                      name="totalCommission"
                      label={'Total Commission'}
                      labelClass={'fw-medium'}
                      type="number"
                      error={false}
                      borderRadius={10}
                      value={totalCommission.toFixed(2)}
                      readOnly
                    />
                    <CustomInput
                      name="difference"
                      label={'Difference'}
                      labelClass={'fw-medium'}
                      type="number"
                      error={false}
                      borderRadius={10}
                      value={difference.toFixed(2)}
                      readOnly
                    />
                  </div>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </>
  );
};

export default NewSpecialComission;
