import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import Skeleton from 'react-loading-skeleton';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomModal from '../../../Components/CustomModal';
import { showToast } from '../../../Components/Toast/Toast';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import {
  deleteReceiptVoucher,
  getReceiptVoucherListing,
} from '../../../Services/Transaction/ReceiptVoucher';
import { isNullOrEmpty, showErrorToast } from '../../../Utils/Utils';

const ViewReceiptVoucher = ({
  searchTerm,
  setDate,
  setWriteTerm,
  setSearchTerm,
  setPageState,
  lastVoucherNumbers,
}) => {
  const queryClient = useQueryClient();
  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const {
    data: { data: [receiptVoucherData] = [] } = {},
    isLoading,
    isFetching,
    isError,
    error,
  } = useQuery({
    queryKey: ['receiptVoucher', searchTerm],
    queryFn: () => getReceiptVoucherListing({ search: searchTerm }),
    staleTime: 1000 * 60 * 5,
  });
  const receiptVoucher = receiptVoucherData?.receipt_vouchers;

  console.log('zxc',receiptVoucher);

  useEffect(() => {
    if (receiptVoucherData?.voucher_no) {
      setDate(receiptVoucherData.date);
      setWriteTerm(receiptVoucherData.voucher_no);
    }
  }, [receiptVoucherData?.voucher_no]);

  // Mutation for delete
  const deleteReceiptVoucherMutation = useMutation({
    mutationFn: (id) => deleteReceiptVoucher(id),
    onSuccess: () => {
      showToast('Receipt Voucher deleted successfully!', 'success');
      queryClient.invalidateQueries(['receiptVoucher', searchTerm]);
      setShowDeleteModal(false);
      setPageState('new');
      setWriteTerm('');
      setSearchTerm('');
      setDate(new Date().toISOString().split('T')[0]);
    },
    onError: (error) => {
      setShowDeleteModal(false);
      showErrorToast(error);
    },
  });

  // Navigation Actions
  const handleEdit = () => {
    setPageState('edit');
  };
  const handleDelete = () => {
    setShowDeleteModal(true);
  };
  const handlePrint = () => {
    if (receiptVoucherData?.pdf_url) {
      window.open(receiptVoucherData?.pdf_url, '_blank');
    }
  };

  if (isLoading) {
    return (
      <div className="d-card ">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
            <div className="row mb-4">
              {Array.from({ length: 8 }).map((_, i) => (
                <div
                  key={i}
                  className="col-12 col-sm-6 mb-3 align-items-center"
                  style={{ height: 56 }}
                >
                  <Skeleton
                    style={{ marginTop: 28 }}
                    duration={1}
                    width={'50%'}
                    baseColor="#ddd"
                    height={22}
                  />
                </div>
              ))}
              <div
                className="col-12 mb-3 align-items-center"
                style={{ height: 56 }}
              >
                <Skeleton
                  style={{ marginTop: 28 }}
                  duration={1}
                  width={'100%'}
                  baseColor="#ddd"
                  height={22}
                />
              </div>
              {Array.from({ length: 6 }).map((_, i) => (
                <div
                  key={i}
                  className="col-12 col-sm-6 mb-3 align-items-center"
                  style={{ height: 56 }}
                >
                  <Skeleton
                    style={{ marginTop: 28 }}
                    duration={1}
                    width={'50%'}
                    baseColor="#ddd"
                    height={22}
                  />
                </div>
              ))}
              <div
                className="col-12 mb-3 align-items-center"
                style={{ height: 56 }}
              >
                <Skeleton
                  style={{ marginTop: 28 }}
                  duration={1}
                  width={'100%'}
                  baseColor="#ddd"
                  height={22}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <>
        <div className="d-card">
          <p className="text-danger">{error.message}</p>
        </div>
      </>
    );
  }
  if (isNullOrEmpty(receiptVoucher)) {
    return (
      <>
        <div className="d-card">
          <p className="text-danger">
            No Receipt Voucher found for ID {searchTerm}
          </p>
        </div>
      </>
    );
  }

  return (
    <>
      <div className="d-card">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7 mb-4">
            <div className="row">
              {[
                {
                  label: 'Ledger',
                  value: receiptVoucher?.new_ledger,
                },
                {
                  label: 'Account',
                  value: receiptVoucher?.account_details?.title,
                },
                {
                  label: 'Received From',
                  value: receiptVoucher?.received_from?.name,
                },
                {
                  label: 'Mode',
                  value: receiptVoucher?.mode,
                },
                {
                  label: 'Mode Account',
                  value: receiptVoucher?.mode_account_id?.account_name,
                },
                {
                  label: `Party's Bank`,
                  value: receiptVoucher?.party_bank,
                },
                {
                  label: 'Cheque Number',
                  value: receiptVoucher?.cheque_number,
                },
                {
                  label: 'Due Date',
                  value: receiptVoucher?.due_date,
                },
                {
                  label: 'Narration',
                  value: receiptVoucher?.narration,
                },
                {
                  label: 'Currency',
                  value: receiptVoucher?.amount_account_id?.currency_code,
                },
                {
                  label: 'Amount',
                  value: receiptVoucher?.amount,
                },
                {
                  label: 'Commission Type',
                  value: receiptVoucher?.commission_type,
                },
                {
                  label: 'Commission',
                  value: receiptVoucher?.commission
                    ? `${receiptVoucher?.commission}%`
                    : null,
                },
                {
                  label: 'VAT Terms',
                  value: receiptVoucher?.vat_terms,
                },
                {
                  label: 'VAT Amount',
                  value: receiptVoucher?.vat_terms
                    ? receiptVoucher?.vat_amount
                    : null,
                },
                {
                  label: 'Net Total',
                  value: receiptVoucher?.net_total,
                },
                {
                  label: 'Comment',
                  value: receiptVoucher?.comment,
                },
              ].map((x, i) => {
                if (isNullOrEmpty(x.value)) return null;
                return (
                  <div
                    key={i}
                    className={`col-12 ${
                      x.label === 'Comment' || x.label === 'Narration'
                        ? ''
                        : 'col-sm-6'
                    } mb-4`}
                  >
                    <p className="detail-title detail-label-color mb-1">
                      {x.label}
                    </p>
                    <p className="detail-text wrapText mb-0">{x.value}</p>
                  </div>
                );
              })}
            </div>
          </div>
          <div className="col-0 col-xxl-2" />
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
            <div className="row">
              {/* Right side cards */}
              <div className="col-12 mb-5" style={{ maxWidth: '350px' }}>
                {/* <AccountBalanceCard />
                <ExchangeRatesCard rates={MOCK_EXCHANGE_RATES} /> */}
              </div>
            </div>
          </div>
          {receiptVoucherData?.special_commission_text && (
            <p className="wrapText mb-0">
              <span
                className={`${
                  receiptVoucherData?.special_commission_text?.includes(
                    'payable'
                  )
                    ? 'text-danger'
                    : receiptVoucherData?.special_commission_text?.includes(
                        'receivable'
                      )
                    ? 'text-success'
                    : ''
                }`}
              >
                {receiptVoucherData?.special_commission_text}
              </span>
            </p>
          )}
        </div>
      </div>
      <VoucherNavigationBar
        isDisabled={isLoading || isError || isNullOrEmpty(receiptVoucher)}
        actionButtons={[
          { text: 'Edit', onClick: handleEdit },
          { text: 'Delete', onClick: handleDelete, variant: 'secondaryButton' },
          ...(receiptVoucherData?.pdf_url
            ? [
                {
                  text: 'Print',
                  onClick: handlePrint,
                  variant: 'secondaryButton',
                },
              ]
            : []),
        ]}
        loading={isLoading || isFetching}
        onAttachmentClick={() => setShowAttachmentsModal(true)}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />
      {/* Attachements Modal */}
      <CustomModal
        show={showAttachmentsModal}
        close={() => setShowAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          viewOnly
          items={receiptVoucherData}
          closeUploader={() => setShowAttachmentsModal(false)}
        />
      </CustomModal>

      {/* Delete Modal */}
      <CustomModal
        show={showDeleteModal}
        close={() => {
          setShowDeleteModal(false); // Close the modal on cancel
        }}
        action={() => {
          if (receiptVoucherData) {
            deleteReceiptVoucherMutation.mutate(receiptVoucherData.voucher_no);
          }
        }}
        title="Delete"
        description={`Are you sure you want to delete Receipt Voucher ${receiptVoucherData.voucher_no}?`}
        disableClick={deleteReceiptVoucherMutation.isPending}
      />
    </>
  );
};

export default ViewReceiptVoucher;
