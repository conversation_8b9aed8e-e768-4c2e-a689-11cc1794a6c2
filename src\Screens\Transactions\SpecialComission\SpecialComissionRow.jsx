import React from 'react';
import { HiOutlineTrash } from 'react-icons/hi2';
import CustomInput from '../../../Components/CustomInput';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import { serialNum } from '../../../Utils/Utils';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';

const SpecialComissionRow = ({
  row,
  index,
  updateField,
  handleDeleteRow,
  accountData,
  setShowAddLedgerModal,
}) => {
  const getAccountsByTypeOptions = (accountType) => {
    if (!accountType) {
      return [{ label: 'Select Ledger', value: null, isDisabled: true }];
    }

    const { data, loading, error, errorMessage } =
      accountData[accountType] || {};

    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (error) {
      console.error('Unable to fetch Accounts', errorMessage);
      return [{ label: 'Unable to fetch Accounts', value: null }];
    }
    let options =
      data?.map((x) => ({
        value: x?.id,
        label: x?.title,
      })) || [];
    switch (accountType) {
      case 'party':
        options.push({
          label: `Add New PL`,
          value: null,
        });
        break;
      case 'general':
        options.push({
          label: `Add New GL`,
          value: null,
        });
        break;
      case 'walkin':
        options.push({
          label: `Add New WIC`,
          value: null,
        });
        break;
      default:
        break;
    }
    return options;
  };
  // --- //

  return (
    <tr>
      <td>{serialNum(index + 1)}</td>
      <td>
        <SearchableSelect
          options={[
            { label: 'PL', value: 'party' },
            { label: 'GL', value: 'general' },
            { label: 'WIC', value: 'walkin' },
          ]}
          placeholder="Ledger"
          value={row.ledger}
          onChange={(selected) => {
            updateField(row.id, 'ledger', selected.value);
          }}
          borderRadius={10}
        />
      </td>
      <td>
        <SearchableSelect
          options={[
            { label: 'ABC', value: 'abc' },
            { label: 'DEF', value: 'def' },
            { label: 'XYZ', value: 'xyz' },
          ]}
          placeholder="Account"
          value={row.credit_account}
          onChange={(selected) => {
            if (selected.label?.toLowerCase()?.startsWith('add new')) {
              setShowAddLedgerModal(selected.label?.toLowerCase());
            } else {
              updateField(row.id, 'credit_account', selected.value);
            }
          }}
          borderRadius={10}
          minWidth={240}
        />
      </td>
      <td>
        <CustomInput
          type={'text'}
          value={row.narration}
          placeholder="Enter Narration"
          onChange={(e) => updateField(row.id, 'narration', e.target.value)}
          borderRadius={10}
          style={{ minWidth: 300 }}
        />
      </td>
      <td>
        <CustomInput
          type={'text'}
          value={row.percentage}
          placeholder="Percentage"
          rightText={`%`}
          onChange={(e) => updateField(row.id, 'percentage', e.target.value)}
          borderRadius={10}
          style={{ maxWidth: 140 }}
        />
      </td>

      <td>
        <CustomInput
          type={'text'}
          value={row.amount}
          disabled={true}
          placeholder="Amount"
          onChange={(e) => updateField(row.id, 'amount', e.target.value)}
          borderRadius={10}
          style={{ maxWidth: 135 }}
          readOnly
        />
      </td>

      <td>
        <TableActionDropDown
          actions={[
            {
              name: 'Delete',
              icon: HiOutlineTrash,
              onClick: () => handleDeleteRow(row.id),
              className: 'delete',
            },
          ]}
        />
      </td>
    </tr>
  );
};

export default SpecialComissionRow;
