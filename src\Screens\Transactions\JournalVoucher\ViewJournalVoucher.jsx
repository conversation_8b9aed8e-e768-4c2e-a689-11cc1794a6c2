import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import { showToast } from '../../../Components/Toast/Toast';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import withFilters from '../../../HOC/withFilters ';
import {
  deleteJournalVoucher,
  getJournalVoucherListing,
} from '../../../Services/Transaction/JournalVoucher';
import { journalVoucherViewHeaders } from '../../../Utils/Constants/TableHeaders';
import { isNullOrEmpty, showErrorToast } from '../../../Utils/Utils';

const ViewJournalVoucher = ({
  searchTerm,
  setSearchTerm,
  setWriteTerm,
  setPageState,
  setDate,
  lastVoucherNumbers,
}) => {
  const queryClient = useQueryClient();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [attachmentsModal, setAttachmentsModal] = useState(false);

  const {
    data: { data: [journalVoucherData] = [] } = {}, // [journalVoucherData] = destructuring array first item
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['journalVoucher', searchTerm],
    queryFn: () => getJournalVoucherListing({ search: searchTerm }),
  });

  useEffect(() => {
    if (journalVoucherData?.date) {
      setDate(journalVoucherData?.date);
      setWriteTerm(journalVoucherData?.voucher_no);
    }
  }, [journalVoucherData?.date]);
  // Mutation for delete
  const deleteJournalVoucherMutation = useMutation({
    mutationFn: (id) => deleteJournalVoucher(id), // Call the API to delete the package
    onSuccess: () => {
      showToast('Journal Voucher deleted successfully!', 'success');
      queryClient.invalidateQueries(['journalVoucher', searchTerm]);
      setShowDeleteModal(false);
      setPageState('new');
      setWriteTerm('');
      setSearchTerm('');
      setDate(new Date().toISOString().split('T')[0]);
    },
    onError: (error) => {
      showErrorToast(error);
      setShowDeleteModal(false);
    },
  });

  // Navigation Actions
  const handleEdit = () => {
    setPageState('edit');
  };
  const handleDelete = () => {
    setShowDeleteModal(true);
  };
  const handlePrint = () => {
    if (journalVoucherData?.pdf_url) {
      window.open(journalVoucherData?.pdf_url, '_blank');
    }
  };

  if (isError) {
    console.error(error);
  }

  return (
    <>
      <CustomTable
        headers={journalVoucherViewHeaders}
        isPaginated={false}
        isLoading={isLoading}
        hideItemsPerPage
        hideSearch
      >
        {
          <tbody>
            {isError ? (
              <tr>
                <td colSpan={journalVoucherViewHeaders.length}>
                  <p className="text-danger mb-0">
                    Unable to fetch data at this time
                  </p>
                </td>
              </tr>
            ) : (
              isNullOrEmpty(journalVoucherData?.journal_vouchers) && (
                <tr>
                  <td colSpan={journalVoucherViewHeaders.length}>
                    <p className="text-danger mb-0">
                      No Journal Voucher found for ID {searchTerm}
                    </p>
                  </td>
                </tr>
              )
            )}
            {journalVoucherData?.journal_vouchers?.map((item, index) => (
              <tr key={item.id}>
                <td>{index + 1}</td>
                <td>{item.new_ledger}</td>
                <td>{item.account_details?.title}</td>
                <td>{item?.narration}</td>
                <td>{item.currency?.currency_code}</td>
                <td>{item.fc_amount}</td>
                <td>{item.lc_amount}</td>
                <td>{parseFloat(item.rate).toFixed(3)}</td>
                <td>{item.sign}</td>
              </tr>
            ))}
          </tbody>
        }
      </CustomTable>
      <div className="d-flex justify-content-end gap-3 mt-45 mb-5">
        <div className="d-flex flex-column gap-2 mt-1">
          {!isLoading && !isError && !isNullOrEmpty(journalVoucherData) && (
            <>
              <CustomInput
                name="totalDebit"
                label={'Total Debit'}
                labelClass={'fw-medium'}
                type="number"
                showBorders={false}
                error={false}
                borderRadius={10}
                defaultValue={parseFloat(
                  journalVoucherData?.total_debit || 0
                ).toFixed(2)}
                readOnly
              />
              <CustomInput
                name="totalCredit"
                label={'Total Credit'}
                labelClass={'fw-medium'}
                type="number"
                showBorders={false}
                error={false}
                borderRadius={10}
                defaultValue={parseFloat(
                  journalVoucherData?.total_credit || 0
                ).toFixed(2)}
                readOnly
              />
              <CustomInput
                name="difference"
                label={'Difference'}
                labelClass={'fw-medium'}
                type="number"
                showBorders={false}
                error={false}
                borderRadius={10}
                defaultValue={
                  (
                    parseFloat(journalVoucherData?.total_debit) -
                    parseFloat(journalVoucherData?.total_credit)
                  ).toFixed(2) || null
                }
                readOnly
              />
            </>
          )}
        </div>
      </div>
      <VoucherNavigationBar
        isDisabled={isLoading || isError || isNullOrEmpty(journalVoucherData)}
        actionButtons={[
          { text: 'Edit', onClick: handleEdit },
          { text: 'Delete', onClick: handleDelete, variant: 'secondaryButton' },
          { text: 'Print', onClick: handlePrint, variant: 'secondaryButton' },
        ]}
        onAttachmentClick={() => setAttachmentsModal(true)}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />
      {/* Attachements Modal */}
      <CustomModal
        show={attachmentsModal}
        close={() => setAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          viewOnly
          item={journalVoucherData}
          closeUploader={() => setAttachmentsModal(false)}
        />
      </CustomModal>

      {/* Delete Modal */}
      <CustomModal
        show={showDeleteModal}
        close={() => {
          setShowDeleteModal(false);
        }}
        action={() => {
          if (journalVoucherData) {
            deleteJournalVoucherMutation.mutate(journalVoucherData.voucher_no);
          }
        }}
        title="Delete"
        description={`Are you sure you want to delete JV Number ${journalVoucherData?.voucher_no}?`}
        disableClick={deleteJournalVoucherMutation.isPending}
      />
    </>
  );
};

export default withFilters(ViewJournalVoucher);
