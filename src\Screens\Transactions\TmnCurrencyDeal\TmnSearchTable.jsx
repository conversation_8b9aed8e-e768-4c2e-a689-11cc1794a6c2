import React from 'react';
import CustomTable from '../../../Components/CustomTable/CustomTable';

const TmnSearchTable = ({ searchType, tableData, onRowClick }) => {
  const headers = [
    'Date',
    `${searchType === 'buy' ? 'TBN' : 'TSN'} No.`,
    'Ledger Name',
    'Account Name',
    'Beneficiary',
    'Bank',
    `${searchType === 'buy' ? 'Buy' : 'Sell'} FCy`,
    `${searchType === 'buy' ? 'Buy' : 'Sell'} FC Amount`,
    'Rate',
    'Against FCy',
    'Against FC Amount',
    'Commission',
    'VAT',
    'FC Net Total',
    'User ID',
    'Time',
    'Attachment'
  ];

  return (
    <CustomTable
      hasFilters={false}
      setFilters={false}
      headers={headers}
      pagination={{
        total: tableData.length,
        per_page: 10,
        current_page: 1
      }}
      isLoading={false}
      sortKey={false}
      sortOrder={false}
      handleSort={false}
      isPaginated={false}
    >
      <tbody>
        {tableData?.map((row) => (
          <tr key={row.id} style={{ cursor: 'pointer' }}>
            <td>{row.date}</td>
            <td onClick={() => onRowClick(row)}>
              <p className="text-link text-decoration-underline cp mb-0">
                {row.id}
              </p>
            </td>
            <td>{row.ledgerName}</td>
            <td>{row.accountName}</td>
            <td>{row.beneficiary}</td>
            <td>{row.bank}</td>
            <td>{row.buyFCy}</td>
            <td>{row.buyFCAmount}</td>
            <td>{row.rate}</td>
            <td>{row.sellFCy}</td>
            <td>{row.sellFCAmount}</td>
            <td>{row.commissionFCy}</td>
            <td>{row.vatAmount}</td>
            <td>{row.fcNetTotal}</td>
            <td>{row.userId}</td>
            <td>{row.time}</td>
            <td>{row.hasAttachment}</td>
          </tr>
        ))}
      </tbody>
    </CustomTable>
  );
};

export default TmnSearchTable;