import { Form, Formik } from 'formik';
import React, { useCallback, useRef, useState } from 'react';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import { currencyTransferNewHeaders } from '../../../Utils/Constants/TableHeaders';
import CurrencyTransferRow from './CurrencyTransferRow';
import { MOCK_EXCHANGE_RATES, MOCK_EXCHANGE_RATES_NEW } from '../../../Mocks/MockData';
import { currencyTransferValidationSchema } from '../../../Utils/Validations/ValidationSchemas';
import { useNavigate } from 'react-router-dom';
import CustomButton from '../../../Components/CustomButton';
const generateInitialRows = (count) => {
  const rows = {};
  Array.from({ length: count }).forEach(() => {
    const id = crypto.randomUUID();
    rows[id] = {
      id,
      currency: 'DHS',
      amount: '',
      narration: '',
      docType: '',
      docNo: '',
      bank: '',
      city: '',
      code: ''
    };
  });
  return rows;
};
const INITIAL_STATE = generateInitialRows(3);

const NewCurrencyTransfer = ({
  isDisabled = false,
  setIsDisabled,
  setShowAddOfficeLocationModal,
  setShowAddLedgerModal,
  newlyCreatedAccount,
  uploadAttachmentsModal,
  setUploadAttachmentsModal,
  selectedFiles,
  setSelectedFiles,
  setShowMissingCurrencyRateModal,
  setCurrencyToSelect
}) => {
  const navigate = useNavigate();
  const [rows, setRows] = useState(INITIAL_STATE);
  const [totalDebit, setTotalDebit] = useState(0);
  const [totalCredit, setTotalCredit] = useState(0);
  const formikRef = useRef();

  const handleAddRows = () => {
    const newRows = {};
    const id = crypto.randomUUID();
    newRows[id] = {
      id,
      currency: 'DHS',
      amount: '',
      narration: '',
      docType: '',
      docNo: '',
      bank: '',
      city: '',
      code: ''
    };
    setRows((prevRows) => ({ ...prevRows, ...newRows }));
  };

  const handleSubmit = () => {
    const formValues = formikRef.current.values;
    console.log('formValues', formValues);
    console.log('selectedFiles', selectedFiles);
    let payload = {
      ...rows,
    };

    // Remove rows that have empty values
    payload = Object.fromEntries(
      Object.entries(payload).filter(([_, obj]) => {
        return Object.entries(obj).every(([_, v]) => {
          return v !== '' && v !== null && v !== undefined;
        });
      })
    );

    console.log('submit payload:', payload);
  };

  const handleCancel = () => {
    setRows(generateInitialRows(3));
    setIsDisabled(true);
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
  };

  // Handler functions for rows
  const updateField = useCallback((id, field, value) => {
    setRows((prev) => {
      const newRows = {
        ...prev,
        [id]: {
          ...prev[id],
          [field]: value,
        },
      };

      return newRows;
    });
  }, []);

  const handleDeleteRow = (id) => {
    setRows((prevRows) => {
      const newState = { ...prevRows };
      delete newState[id];
      return newState;
    });
  };

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={{
            debitLedger: '',
            debitAccount: '',
            creditLedger: '',
            creditAccount: '',
            accountTitle: '',
          }}
          validationSchema={currencyTransferValidationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, handleChange, handleBlur, setFieldValue }) => (
            <Form>
              <div className="row">
                <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
                  <div className="row mb-4">
                    {/* Debit Account Section */}
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'debitLedger'}
                        label={'Select Ledger'}
                        options={[
                          { label: 'Party Ledger', value: 'pl' },
                          { label: 'General Ledger', value: 'gl' },
                          { label: 'Walk-in Customer', value: 'wic' },
                        ]}
                        isDisabled={isDisabled}
                        placeholder={'Select Ledger'}
                        value={values.debitLedger}
                        onChange={(selected) => {
                          setFieldValue('debitLedger', selected.value);
                          setFieldValue('debitAccount', ''); // Reset account when ledger changes
                        }}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'debitAccount'}
                        label={'Select Debit Account'}
                        options={[
                          ...(values.debitLedger === 'pl' ? [{ label: 'Add New PL', value: 'add_pl' }] : []),
                          ...(values.debitLedger === 'gl' ? [{ label: 'Add New GL', value: 'add_gl' }] : []),
                          ...(values.debitLedger === 'wic' ? [{ label: 'Add New WIC', value: 'add_wic' }] : []),
                        ]}
                        isDisabled={isDisabled || !values.debitLedger}
                        placeholder={'Select Debit Account'}
                        value={values.debitAccount}
                        onChange={(selected) => {
                          if (selected.value?.startsWith('add_')) {
                            setShowAddLedgerModal(selected.value.replace('add_', 'add new '));
                          } else {
                            setFieldValue('debitAccount', selected.value);
                          }
                        }}
                        onBlur={handleBlur}
                      />
                    </div>

                    {/* Credit Account Section */}
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'creditLedger'}
                        label={'Select Ledger'}
                        options={[
                          { label: 'Party Ledger', value: 'pl' },
                          { label: 'General Ledger', value: 'gl' },
                          { label: 'Walk-in Customer', value: 'wic' },
                        ]}
                        isDisabled={isDisabled}
                        placeholder={'Select Ledger'}
                        value={values.creditLedger}
                        onChange={(selected) => {
                          setFieldValue('creditLedger', selected.value);
                          setFieldValue('creditAccount', ''); // Reset account when ledger changes
                        }}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'creditAccount'}
                        label={'Select Credit Account'}
                        options={[
                          ...(values.creditLedger === 'pl' ? [{ label: 'Add New PL', value: 'add_pl' }] : []),
                          ...(values.creditLedger === 'gl' ? [{ label: 'Add New GL', value: 'add_gl' }] : []),
                          ...(values.creditLedger === 'wic' ? [{ label: 'Add New WIC', value: 'add_wic' }] : []),
                        ]}
                        isDisabled={isDisabled || !values.creditLedger}
                        placeholder={'Select Credit Account'}
                        value={values.creditAccount}
                        onChange={(selected) => {
                          if (selected.value?.startsWith('add_')) {
                            setShowAddLedgerModal(selected.value.replace('add_', 'add new '));
                          } else {
                            setFieldValue('creditAccount', selected.value);
                          }
                        }}
                        onBlur={handleBlur}
                      />
                    </div>

                    {/* Account Title */}
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'accountTitle'}
                        label={'Account Title'}
                        options={[]}
                        isDisabled={isDisabled}
                        placeholder={'Show'}
                        value={values.accountTitle}
                        onChange={(selected) => {
                          setFieldValue('accountTitle', selected.value);
                        }}
                        onBlur={handleBlur}
                      />
                    </div>
                  </div>
                </div>
                <div className="col-0  col-xxl-2" />
                <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
                  <div className="row">
                    {/* Right side cards */}
                    <div className="col-12 mb-2" style={{ maxWidth: '350px' }}>
                      <AccountBalanceCard />
                    </div>
                    <div className="col-12 mb-2" style={{ maxWidth: '350px' }}>
                      <AccountBalanceCard />
                    </div>
                    <div className="col-12 mb-4" style={{ maxWidth: '350px' }}>
                      <h6 className="mb-2">Live Exchange Rates Against Base Currency</h6>
                      <div className="d-card account-balance-card">
                        <div className="d-flex justify-content-between align-items-center mb-3">
                          <div className="d-flex align-items-center account-name w-100">
                            <span className="me-2" style={{ color: '#6B7280' }}>Inverse</span>
                            <div className="form-check form-switch">
                              <input className="form-check-input" type="checkbox" style={{ cursor: 'pointer' }} />
                            </div>
                          </div>
                        </div>
                        <table className="w-100">
                          <thead>
                            <tr style={{ borderBottom: '1px solid #E5E7EB' }}>
                              <th style={{ padding: '8px 0', color: '#6B7280', fontWeight: '500' }}>FCy</th>
                              <th style={{ padding: '8px 0', color: '#6B7280', fontWeight: '500' }}>Rates</th>
                              <th style={{ padding: '8px 0', color: '#6B7280', fontWeight: '500' }}>Change (24h)</th>
                            </tr>
                          </thead>
                          <tbody>
                            {MOCK_EXCHANGE_RATES.map((rate, index) => (
                              <tr key={index}>
                                <td style={{ padding: '8px 0' }}>{rate.currency}</td>
                                <td style={{ padding: '8px 0' }}>{rate.rate}</td>
                                <td style={{
                                  padding: '8px 0',
                                  color: rate.isPositive ? '#22C55E' : '#EF4444'
                                }}>
                                  {rate.change}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                    <div className="col-12 mb-5" style={{ maxWidth: '350px' }}>
                      <div className="d-card account-balance-card">
                        <table className="w-100">
                          <thead>
                            <tr style={{ borderBottom: '1px solid #E5E7EB' }}>
                              <th style={{ padding: '8px 0', color: '#6B7280', fontWeight: '500' }}>Currency</th>
                              <th style={{ padding: '8px 0', color: '#6B7280', fontWeight: '500' }}>Net Total</th>
                            </tr>
                          </thead>
                          <tbody>
                            {MOCK_EXCHANGE_RATES_NEW.map((rate, index) => (
                              <tr key={index}>
                                <td style={{ padding: '8px 0' }}>{rate.currency}</td>
                                <td style={{ padding: '8px 0' }}>{rate.net_total}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
                <CustomTable
                  displayCard={false}
                  headers={currencyTransferNewHeaders}
                  isPaginated={false}
                  className={'inputTable'}
                  hideSearch
                  hideItemsPerPage
                >
                  <tbody>
                    {Object.values(rows).map((row, index) => (
                      <CurrencyTransferRow
                        key={row.id}
                        row={row}
                        index={index}
                        isDisabled={isDisabled}
                        handleDeleteRow={handleDeleteRow}
                        updateField={updateField}
                        setShowMissingCurrencyRateModal={setShowMissingCurrencyRateModal}
                        setCurrencyToSelect={setCurrencyToSelect}
                      />
                    ))}
                  </tbody>
                </CustomTable>
                <div className="my-3">
                  <CustomButton
                    text="Add Special Commission"
                    variant="secondary"
                    disabled={isDisabled}
                    type="button"
                    className="w-auto px-5"
                    onClick={() => navigate('/transactions/special-comission')}
                  />
                </div>
                <div className="d-flex flex-wrap justify-content-between mt-3 mb-5">
                  <div className="d-inline-block mt-3">
                    <CustomCheckbox
                      label="Account Balance"
                      disabled={isDisabled}
                      style={{ border: 'none', margin: 0 }}
                      onChange={() => { }}
                    />
                    <CustomCheckbox
                      label="Print"
                      disabled={isDisabled}
                      style={{ border: 'none', margin: 0 }}
                    />
                  </div>
                  <div className="d-flex flex-column gap-2 mt-1 debit-credit-inputs">
                    <CustomInput
                      name="totalDebit"
                      label={'Total Debit'}
                      labelClass={'fw-medium'}
                      type="number"
                      error={false}
                      borderRadius={10}
                      value={totalDebit.toFixed(2)}
                      readOnly
                    />
                    <CustomInput
                      name="totalCredit"
                      label={'Total Credit'}
                      labelClass={'fw-medium'}
                      type="number"
                      error={false}
                      borderRadius={10}
                      value={totalCredit.toFixed(2)}
                      readOnly
                    />
                  </div>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
      <VoucherNavigationBar
        isDisabled={isDisabled}
        actionButtons={[
          { text: 'Add Rows', onClick: handleAddRows },
          { text: 'Save', onClick: handleSubmit },
          { text: 'Cancel', onClick: handleCancel, variant: 'secondaryButton' },
        ]}
        onAttachmentClick={() => setUploadAttachmentsModal(true)}
        lastVoucherHeading="Last FSN Number"
        lastVoucherNumber={23}
      />
      {/* Upload Attachements Modal */}
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          uploadOnly
          getUploadedFiles={setSelectedFiles}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>
    </>
  );
};

export default NewCurrencyTransfer;
