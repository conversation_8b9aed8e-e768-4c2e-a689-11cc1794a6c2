
import axiosInstance from '../../Config/axiosConfig';
import { buildFormData } from '../../Utils/Utils';


// CREATE
export const createBankTransaction = async (formData) => {
  // const payload = new FormData();
  // buildFormData(payload, formData);
  try {
    const response = await axiosInstance.post(
      '/user-api/bank-transaction',
      formData
    );
    const {
      data: { message, status, detail },
    } = response;
    return { message, status, detail }; // Assume this returns the success object
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};


// GET ACCOUNTS BY TYPE -- party,walkin,general
export const getBankTransactionListing = async (params) => {
  try {
    const { data } = await axiosInstance.get(`/user-api/bank-transaction`, {
      params,
    });

    return data.detail; // Assume this returns success obj

  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};

export const updateBankTransaction = async (id,formData) => {
  try {
    // const payload = new FormData();
    // buildFormData(payload, formData);
    console.log('formData',formData)

    const response = await axiosInstance.post(
      `/user-api/bank-transaction/${id}`,
      formData
    );
    const {
      data: { message, status, detail },
    } = response;
    console.log('response',response)
    return { message, status, detail }; // Assume this returns the success object
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};



// GET COUNTRIES
export const getCountriesListing = async () => {
  try {
    const { data } = await axiosInstance.get(
      `/user-api/bank-transaction/country`
    );
    return data.detail; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};

export const getBankTransactionChequeNumberByBank = async (Bank) => {
  try {
    const { data } = await axiosInstance.get(
      `/user-api/bank-transaction/cheques?bank_id=${Bank}`
    );
    return data.detail; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};


export const getVoucherNumber = async (voucher_no = '' , transaction_type) => {
  try {
    const { data } = await axiosInstance.get(
      `/user-api/internal-payment-voucher/voucher-number?voucher_no=${voucher_no}?transaction_type=${transaction_type}`
    );
    return data.detail; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};
export const deleteBankTransaction = async (id,transactionType) => {
  try {
    const { data: { message, status } = {} } = await axiosInstance.delete(
      `/user-api/bank-transaction/${id}?transaction_type=${transactionType}`
    );
    return { message, status };
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};