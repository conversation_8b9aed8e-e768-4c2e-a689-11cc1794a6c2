import React from 'react';
import { Form, Formik } from 'formik';
import { FaChevronLeft, FaChevronRight, FaPaperclip } from 'react-icons/fa';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import { foreignCurrencyDealValidationSchema } from '../../../Utils/Validations/ValidationSchemas';
import { useNavigate } from 'react-router-dom';
import CustomModal from '../../../Components/CustomModal';
import AddAllocationDetailsForm from '../../../Components/AddAllocationDetailsForm/AddAllocationDetailsForm';

const TmnDealForm = ({
  initialValues,
  isDisabled,
  onSave,
  onCancel,
  searchType,
  onAttachment,
  handleAccountChange,
  setShowMissingCurrencyRateModal,
  setShowBeneficiaryAccountModal,
  setCurrencyToSelect,
  setShowAddAllocationModal,
  showAddAllocationModal,
  currencyOptions,
}) => {
  const navigate = useNavigate();
  const getAccountOptions = (ledgerType) => {
    switch (ledgerType) {
      case 'pl':
        return [
          { label: 'Account 1', value: 'account_1' },
          { label: 'Add PL Ledger', value: 'add_new_pl' },
        ];
      case 'gl':
        return [
          { label: 'Account 1', value: 'account_1' },
          { label: 'Add GL Ledger', value: 'add_new_gl' },
        ];
      case 'wic':
        return [
          { label: 'Account 1', value: 'account_1' },
          { label: 'Add WIC Ledger', value: 'add_new_wic' },
        ];
      default:
        return [];
    }
  };

  const handleAccountSelection = (selected, accountType, setFieldValue) => {
    handleAccountChange(selected, accountType, setFieldValue);
  };

  const handleCurrencySelection = (selected, setFieldValue) => {
    if (selected.value === 'ETH') {
      setCurrencyToSelect('ETH');
      setShowMissingCurrencyRateModal(true);
    } else {
      setFieldValue('buyFCy', selected.value);
    }
  };

  return (
    <>
      <Formik
        initialValues={initialValues}
        validationSchema={foreignCurrencyDealValidationSchema}
        onSubmit={onSave}
        enableReinitialize={true}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          setFieldValue,
        }) => (
          <Form>
            <div className="d-flex gap-3 mb-3 flex-wrap">
              <div className="flex-grow-1">
                <label>Type</label>
                <SearchableSelect
                  name="type"
                  options={[
                    { label: 'Buy', value: 'buy' },
                    { label: 'Sell', value: 'sell' },
                  ]}
                  value={values.type}
                  onChange={(selected) => setFieldValue('type', selected.value)}
                  placeholder="Select Type"
                  isDisabled={isDisabled}
                  error={touched.type && errors.type}
                />
              </div>

              <div className="flex-grow-1">
                <label>Mode</label>
                <SearchableSelect
                  name="mode"
                  options={[
                    { label: 'On A/C', value: 'onAC' },
                    { label: 'Cash', value: 'cash' },
                    { label: 'Cheque', value: 'cheque' },
                  ]}
                  value={values.mode}
                  onChange={(selected) => setFieldValue('mode', selected.value)}
                  placeholder="Select Mode"
                  isDisabled={isDisabled}
                  error={touched.mode && errors.mode}
                />
              </div>
            </div>

            <div className="d-flex gap-3 mb-3 flex-wrap">
              <div className="flex-grow-1">
                <label>Account</label>
                <div className="d-flex gap-3 flex-wrap">
                  <div className="flex-grow-1">
                    <SearchableSelect
                      name="account.ledger"
                      options={[
                        { label: 'PL', value: 'pl' },
                        { label: 'GL', value: 'gl' },
                        { label: 'WIC', value: 'wic' },
                      ]}
                      value={values.account.ledger}
                      onChange={(selected) =>
                        setFieldValue('account.ledger', selected.value)
                      }
                      placeholder="Select Ledger"
                      isDisabled={isDisabled}
                      error={touched.account?.ledger && errors.account?.ledger}
                      minWidth={0}
                    />
                  </div>
                  <div className="flex-grow-1">
                    <SearchableSelect
                      name="account.account"
                      options={getAccountOptions(values.account.ledger)}
                      value={values.account.account}
                      onChange={(selected) =>
                        handleAccountSelection(
                          selected,
                          'account',
                          setFieldValue
                        )
                      }
                      placeholder="Select Account"
                      isDisabled={isDisabled || !values.account.ledger}
                      error={
                        touched.account?.account && errors.account?.account
                      }
                      minWidth={0}
                    />
                  </div>
                </div>
              </div>
              <div className="flex-grow-1">
                <label>Beneficiary</label>
                <SearchableSelect
                  name="beneficiary"
                  options={[
                    { label: 'Beneficiary 1', value: 'ben1' },
                    {
                      label: 'Add Beneficiary Account',
                      value: 'add_beneficiary',
                    },
                  ]}
                  value={values.beneficiary}
                  onChange={(selected) => {
                    if (selected.value === 'add_beneficiary') {
                      setShowBeneficiaryAccountModal(true);
                    } else {
                      setFieldValue('beneficiary', selected.value);
                    }
                  }}
                  placeholder="Select Beneficiary"
                  isDisabled={isDisabled}
                  error={touched.beneficiary && errors.beneficiary}
                />
              </div>
            </div>

            <div className="d-flex gap-3 mb-3 flex-wrap">
              <div className="flex-grow-1">
                <label>Bank</label>
                <CustomInput
                  type="text"
                  name="bank"
                  value={values.bank}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  placeholder="Enter Bank"
                  disabled={isDisabled}
                  error={touched.bank && errors.bank}
                />
              </div>

              <div className="flex-grow-1">
                <label>Bank Account</label>
                <CustomInput
                  type="text"
                  name="bankAccount"
                  value={values.bankAccount}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  placeholder="Enter Bank Account"
                  disabled={isDisabled}
                  error={touched.bankAccount && errors.bankAccount}
                />
              </div>
            </div>

            <div className="d-flex gap-3 mb-3 flex-wrap">
              <div className="flex-grow-1">
                <label>City</label>
                <CustomInput
                  type="text"
                  name="city"
                  value={values.city}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  placeholder="Enter City"
                  disabled={isDisabled}
                  error={touched.city && errors.city}
                />
              </div>

              <div className="flex-grow-1">
                <label>Purpose</label>
                <SearchableSelect
                  name="purpose"
                  options={[
                    { label: 'Business', value: 'business' },
                    { label: 'Personal', value: 'personal' },
                    { label: 'Investment', value: 'investment' },
                  ]}
                  value={values.purpose}
                  onChange={(selected) =>
                    setFieldValue('purpose', selected.value)
                  }
                  placeholder="Select Purpose"
                  isDisabled={isDisabled}
                  error={touched.purpose && errors.purpose}
                />
              </div>
            </div>

            <div className="d-flex gap-3 mb-3 flex-wrap">
              <div className="flex-grow-1">
                <label>Buy/Sell FCy</label>
                <div className="d-flex gap-3 flex-wrap">
                  <div className="flex-grow-1">
                    <SearchableSelect
                      name="buyFCy"
                      options={currencyOptions}
                      value={values.buyFCy}
                      onChange={(selected) =>
                        handleCurrencySelection(selected, setFieldValue)
                      }
                      placeholder="Select Currency"
                      isDisabled={isDisabled}
                      error={touched.buyFCy && errors.buyFCy}
                    />
                  </div>
                  <div className="flex-grow-1">
                    <CustomInput
                      type="number"
                      name="buyAmount"
                      value={values.buyAmount}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      placeholder="Enter Amount"
                      disabled={isDisabled}
                      error={touched.buyAmount && errors.buyAmount}
                    />
                  </div>
                </div>
              </div>

              <div className="flex-grow-1">
                <label>Rate Type</label>
                <SearchableSelect
                  name="rateType"
                  options={[
                    { label: 'Fixed', value: 'fixed' },
                    { label: 'Floating', value: 'floating' },
                    { label: 'Special', value: 'special' },
                  ]}
                  value={values.rateType}
                  onChange={(selected) =>
                    setFieldValue('rateType', selected.value)
                  }
                  placeholder="Select Rate Type"
                  isDisabled={isDisabled}
                  error={touched.rateType && errors.rateType}
                />
              </div>
            </div>

            <div className="d-flex gap-3 mb-3 flex-wrap">
              <div className="flex-grow-1">
                <label>AG FCy</label>
                <div className="d-flex gap-3 flex-wrap">
                  <div className="flex-grow-1">
                    <SearchableSelect
                      name="agFCy"
                      options={[
                        { label: 'USD', value: 'USD' },
                        { label: 'EUR', value: 'EUR' },
                        { label: 'GBP', value: 'GBP' },
                      ]}
                      value={values.agFCy}
                      onChange={(selected) =>
                        setFieldValue('agFCy', selected.value)
                      }
                      placeholder="Select Currency"
                      isDisabled={isDisabled}
                      error={touched.agFCy && errors.agFCy}
                    />
                  </div>
                  <div className="flex-grow-1">
                    <CustomInput
                      type="number"
                      name="agFCAmount"
                      value={values.agFCAmount}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      placeholder="Enter Amount"
                      disabled={isDisabled}
                      error={touched.agFCAmount && errors.agFCAmount}
                    />
                  </div>
                </div>
              </div>

              {values.type == 'buy' && (
                <div className="flex-grow-1">
                  <label>Commission</label>
                  <CustomInput
                    type="number"
                    name="commission"
                    value={values.commission}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="Enter Commission"
                    disabled={isDisabled}
                    error={touched.commission && errors.commission}
                  />
                </div>
              )}
            </div>

            <div className="d-flex gap-3 mb-3 flex-wrap">
              {values.type == 'buy' && (
                <div className="flex-grow-1">
                  <label>VAT Amount</label>
                  <CustomInput
                    type="number"
                    name="vatAmount"
                    value={values.vatAmount}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="Enter VAT Amount"
                    disabled={isDisabled}
                    error={touched.vatAmount && errors.vatAmount}
                  />
                </div>
              )}
              <div className="flex-grow-1">
                <label>Total</label>
                <CustomInput
                  type="number"
                  name="total"
                  value={values.total}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  placeholder="Enter Total"
                  disabled={isDisabled}
                  error={touched.total && errors.total}
                />
              </div>
            </div>

            <div className="d-flex">
              <CustomButton
                text="Add Special Commission"
                variant="secondary"
                disabled={isDisabled}
                className="mb-3"
                onClick={() => navigate('/transactions/special-comission')}
              />
            </div>

            <h6>Allocation Detail</h6>
            <CustomButton
              text="Add Allocation"
              type="button"
              variant="secondary"
              disabled={isDisabled}
              className="mb-3 w-auto px-5"
              onClick={() => setShowAddAllocationModal(true)}
            />

            <div className="d-flex gap-3 align-items-center mb-3">
              {!isDisabled && (
                <>
                  <CustomButton text="Save" type="submit" />
                  <CustomButton
                    text="Cancel"
                    className="secondaryButton"
                    onClick={onCancel}
                    type="button"
                  />
                </>
              )}
              <div className="ms-auto d-flex gap-2">
                <FaChevronLeft size={20} style={{ cursor: 'pointer' }} />
                <FaChevronRight size={20} style={{ cursor: 'pointer' }} />
                <FaPaperclip
                  size={20}
                  style={{ cursor: 'pointer' }}
                  onClick={onAttachment}
                />
              </div>
            </div>

            <div className="mt-3 d-inline-block">
              <CustomCheckbox
                label="Account Balance"
                style={{ border: 0 }}
                disabled={isDisabled}
              />
              <CustomCheckbox
                label="Print"
                disabled={isDisabled}
                style={{ border: 0 }}
              />
            </div>

            <div className="mt-2">
              Last {searchType === 'buy' ? 'TBN' : 'TSN'} No: 05
            </div>
          </Form>
        )}
      </Formik>

      <CustomModal
        show={!!showAddAllocationModal}
        close={() => setShowAddAllocationModal('')}
        size="xl"
        style={{ minHeight: '812px' }}
      >
        <AddAllocationDetailsForm
          inPopup
          onSuccess={(newlyCreatedAccount) => {
            console.log(newlyCreatedAccount);
            setShowAddAllocationModal('');
          }}
          onCancel={() => setShowAddAllocationModal('')}
        />
      </CustomModal>
    </>
  );
};

export default TmnDealForm;
