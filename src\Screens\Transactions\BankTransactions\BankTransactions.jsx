import React, { useState, useRef, useEffect } from 'react';
import {
  FaChevronLeft,
  FaChevronRight,
  FaMagnifyingGlass,
  FaPaperclip,
} from 'react-icons/fa6';
import CustomButton from '../../../Components/CustomButton';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import CustomModal from '../../../Components/CustomModal';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import ChartOfAccountForm from '../../../Components/ChartOfAccountForm/ChartOfAccountForm';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  MOCK_DEPOSIT_DATA,
  MOCK_INWARD_TT_DATA,
  MOCK_EXCHANGE_RATES,
  MOCK_TRANSACTIONS,
  MOCK_WITHDRAWAL_DATA,
  supportLogsData,
} from '../../../Mocks/MockData';
import PartyLedgerForm from '../../../Components/PartyLedgerForm/PartyLedgerForm';
import WalkInCustomerForm from '../../../Components/WalkInCustomerForm/WalkInCustomerForm';
import withModal from '../../../HOC/withModal';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import {
  depositTableHeaders,
  inwardTTTableHeaders,
  withdrawalTableHeaders,
} from '../../../Utils/Constants/TableHeaders';
import { MOCK_CURRENT_ACCOUNT } from '../../../Mocks/MockData';
import { MOCK_SAVINGS_ACCOUNT } from '../../../Mocks/MockData';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import BackButton from '../../../Components/BackButton';
import withFilters from '../../../HOC/withFilters ';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  getPaymentVoucherMode,
  updateJournalVoucher,
} from '../../../Services/Transaction/JournalVoucher';
import {
  getAccountsbyType,
  getInternalPaymentVoucherChequeNumberByBank,
  getIPVoucherNumber,
  updateInternalPaymentVoucher,
} from '../../../Services/Transaction/InternalPaymentVoucher';
import {
  useBanksList,
  getCurrencyOptions,
  getCountryOptions,
  formatDate,
  showErrorToast,
} from '../../../Utils/Utils';
import {
  createBankTransaction,
  deleteBankTransaction,
  getBankTransactionChequeNumberByBank,
  getBankTransactionListing,
  getVoucherNumber,
  updateBankTransaction,
} from '../../../Services/Transaction/BankTransactions';
import useSettingsStore from '../../../Stores/SettingsStore';
import { showToast } from '../../../Components/Toast/Toast';
import useFormStore from '../../../Stores/FormStore';
import { deleteReceiptVoucher } from '../../../Services/Transaction/ReceiptVoucher.js';

const validationSchema = Yup.object().shape({
  transactionType: Yup.string().required('Transaction type is required'),
  chequeNumber: Yup.string().required('Cheque number is required'),
  fromAccount: Yup.string().required('From account is required'),
  toAccount: Yup.string().when('transactionType', {
    is: (type) => type !== 'inward_tt',
    then: Yup.string().required('To account is required'),
  }),
  amount: Yup.number()
    .required('Amount is required')
    .positive('Amount must be positive'),
  currency: Yup.string().required('Currency is required'),
  narration: Yup.string().required('Narration is required'),

  // Inward TT specific validations
  ledger: Yup.string().when('transactionType', {
    is: 'inward_tt',
    then: Yup.string().required('Ledger is required'),
  }),
  bank: Yup.string().when('transactionType', {
    is: 'inward_tt',
    then: Yup.string().required('Bank is required'),
  }),
  commissionType: Yup.string().when('transactionType', {
    is: 'inward_tt',
    then: Yup.string().required('Commission type is required'),
  }),
  commissionPercentage: Yup.number().when('transactionType', {
    is: 'inward_tt',
    then: Yup.number().min(0, 'Commission percentage must be positive'),
  }),
  commissionAmount: Yup.number().when('transactionType', {
    is: 'inward_tt',
    then: Yup.number().min(0, 'Commission amount must be positive'),
  }),
  country: Yup.string().when('transactionType', {
    is: 'inward_tt',
    then: Yup.string().required('Country is required'),
  }),
});

const BankTransactions = ({
  showModal,
  closeModal,
  filters,
  pagination,
  updatePagination,
}) => {
  usePageTitle('Bank Transactions');
  const queryClient = useQueryClient();
  const [isDisabled, setIsDisabled] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [transactionType, setTransactionType] = useState('deposit');
  const [chequeNumber, setChequeNumber] = useState('');
  const [fromAccount, setFromAccount] = useState('');
  const [toAccount, setToAccount] = useState('');
  const [amount, setAmount] = useState('');
  const [currency, setCurrency] = useState('DHS');
  const [narration, setNarration] = useState('');
  const [headerTransactionType, setHeaderTransactionType] = useState('deposit');
  const [uploadAttachmentsModal, setUploadAttachmentsModal] = useState(false);
  const [addedAttachment, setAddedAttachment] = useState(null);
  const [newlyCreatedAccount, setNewlyCreatedAccount] = useState(null);
  const [showAddLedgerModal, setShowAddLedgerModal] = useState('');
  const [showMissingCurrencyRateModal, setShowMissingCurrencyRateModal] =
    useState(false);
  const [currencyToSelect, setCurrencyToSelect] = useState(null);

  // Add new state for showing the right cards
  const [showRightCards, setShowRightCards] = useState(false);

  // Add new states for inward TT specific fields
  const [bank, setBank] = useState('');
  const [commissionType, setCommissionType] = useState('');
  const [commissionPercentage, setCommissionPercentage] = useState('');
  const [commissionAmount, setCommissionAmount] = useState('');
  const [country, setCountry] = useState('');
  const [selectedBank, setSelectedBank] = useState(null);
  const [transactionDetail, setTransactionDetail] = useState(null);
  // Add new state for ledger
  const [ledger, setLedger] = useState('');

  const headerTransactionTypeValue =
    headerTransactionType === 'inward_tt' ? 'inward' : headerTransactionType;
  const { state } = useLocation();
  const [pageState, setPageState] = useState(state?.pageState || 'new');

  const [lastVoucherNumbers, setLastVoucherNumbers] = useState({
    heading: 'Last Voucher Number: ',
    current: '',
    previous: '',
    next: '',
    isLoadingVoucherNumber: false,
    isErrorVoucherNumber: false,
    errorVoucherNumber: null,
  });

  // Get last voucher number //
  const {
    data: voucherNumber,
    isLoading: isLoadingVoucherNumber,
    isError: isErrorVoucherNumber,
    error: errorVoucherNumber,
  } = useQuery({
    queryKey: ['voucherNumber', searchTerm],
    queryFn: () =>
      getVoucherNumber({
        search: searchTerm,
        transaction_type:
          headerTransactionTypeValue === 'inward_tt'
            ? 'inward'
            : headerTransactionTypeValue,
      }),
    refetchOnWindowFocus: false,
    retry: 1,
  });

  useEffect(() => {
    setLastVoucherNumbers({
      heading: 'Last Voucher Number: ',
      last: voucherNumber?.default_voucher_no,
      current: voucherNumber?.current_voucher_no,
      previous: voucherNumber?.previous_voucher_no,
      next: voucherNumber?.next_voucher_no,
      isLoadingVoucherNumber: isLoadingVoucherNumber,
      isErrorVoucherNumber: isErrorVoucherNumber,
      errorVoucherNumber: errorVoucherNumber,
    });
  }, [
    voucherNumber,
    isLoadingVoucherNumber,
    isErrorVoucherNumber,
    errorVoucherNumber,
  ]);

  console.log('headerTransactionTypeValue', headerTransactionTypeValue);
  console.log('voucherNumber', voucherNumber);

  const currencyOptions = getCurrencyOptions();
  const countryOptions = getCountryOptions();

  console.log('currencyOptions', currencyOptions);

  const banksList = useBanksList();

  // Access the form store
  const {
    saveFormValues,
    getFormValues,
    hasFormValues,
    clearFormValues,
    setLastVisitedPage,
    getLastVisitedPage,
    clearLastVisitedPage,
  } = useFormStore();

  // Update errors state to include new fields and ledger
  const [errors, setErrors] = useState({
    transactionType: '',
    chequeNumber: '',
    fromAccount: '',
    toAccount: '',
    amount: '',
    narration: '',
    bank: '',
    commissionType: '',
    commissionPercentage: '',
    commissionAmount: '',
    country: '',
    ledger: '',
  });

  const initialValues = {
    transactionType: 'deposit',
    chequeNumber: '',
    fromAccount: '',
    toAccount: '',
    amount: '',
    currency: currency || '',
    narration: '',
    ledger: '',
    bank: '',
    commissionType: '',
    commissionPercentage: '',
    commissionAmount: '',
    country: '',
    date: new Date().toISOString().split('T')[0],
  };

  const formId = 'bank-transaction'; // Unique identifier for this form

  // Add formikRef near the top of the component
  const formikRef = useRef();

  const { getPrintSettings } = useSettingsStore();

  // Add new state for showing transaction details
  const [showTransactionDetails, setShowTransactionDetails] = useState(false);
  const [currentTransaction, setCurrentTransaction] = useState(null);
  const [depositData, setDepositData] = useState([]);
  const [withDrawlData, setWithDrawlData] = useState([]);
  const [inwardData, setInwardData] = useState([]);
  const [addedAttachments, setAddedAttachments] = useState(null);

  // Add these new states
  const [showDepositTable, setShowDepositTable] = useState(false);
  const [depositFilters, setDepositFilters] = useState({
    page: 1,
    per_page: 10,
  });
  const [sortKey, setSortKey] = useState('');
  const [sortOrder, setSortOrder] = useState('asc');

  // Add this handler function
  const handleSort = (key) => {
    setSortKey(key);
    setSortOrder((current) => (current === 'asc' ? 'desc' : 'asc'));
  };

  const getSearchPlaceholder = (type) => {
    switch (type) {
      case 'withdrawal':
        return 'Search BWV';
      case 'inward_tt':
        return 'Search BITTV';
      default:
        return 'Search BDV';
    }
  };

  const getLastNumberText = (type) => {
    switch (type) {
      case 'withdrawal':
        return 'Last BWV Number: ' + searchTerm;
      case 'inward_tt':
        return 'Last BITTV Number: ' + searchTerm;
      default:
        return 'Last BDV Number: ' + searchTerm;
    }
  };

  // Modify the New button click handler
  const handleNewClick = () => {
    setIsDisabled(false);
    setShowRightCards(true);
  };

  // Validation function
  const validateForm = () => {
    let isValid = true;
    const newErrors = {};

    if (!transactionType) {
      newErrors.transactionType = 'Transaction type is required';
      isValid = false;
    }

    // if (!chequeNumber) {
    //   newErrors.chequeNumber = 'Cheque number is required';
    //   isValid = false;
    // }

    if (!fromAccount) {
      newErrors.fromAccount = 'From account is required';
      isValid = false;
    }

    // if (!toAccount) {
    //   newErrors.toAccount = 'To account is required';
    //   isValid = false;
    // }

    if (!amount) {
      newErrors.amount = 'Amount is required';
      isValid = false;
    } else if (isNaN(amount) || parseFloat(amount) <= 0) {
      newErrors.amount = 'Please enter a valid amount';
      isValid = false;
    }

    if (!narration.trim()) {
      newErrors.narration = 'Narration is required';
      isValid = false;
    }

    // if (!ledger) {
    //   newErrors.ledger = 'Ledger is required';
    //   isValid = false;
    // }

    alert(JSON.stringify(newErrors));

    console.log(newErrors);
    setErrors(newErrors);
    return isValid;
  };

  // Modify the submit handler
  const handleSubmit = async () => {
    if (!formikRef.current) return;
    // Validate the form

    setAddedAttachments(addedAttachments || null);

    const formValues = formikRef.current.values;

    //  if (validateForm()) {
    // Add your submission logic here

    console.log({
      date,
      transactionType,
      chequeNumber,
      fromAccount,
      toAccount,
      amount,
      currency,
      narration,
    });

    let payload = {
      date,
      currency_id: formValues.currency,
      country_id: formValues.country,
      transaction_type:
        formValues.transactionType === 'inward_tt'
          ? 'inward'
          : formValues.transactionType,
      from_account_id: formValues.fromAccount,
      to_account_id: formValues.toAccount ?? null,
      bank_id: formValues.bank ?? null,
      cheque_number: formValues.chequeNumber ?? null,
      amount: parseFloat(formValues.amount),
      commission_type: formValues.commissionType ?? null,
      commission_percentage: formValues.commissionPercentage ?? null,
      commission_value: formValues.commissionAmount ?? null,
      narration: formValues.narration ?? null,
      ...addedAttachments,
    };

    console.log('values', payload);

    //for update
    if (searchTerm > 0) {
      console.log('About to mutate:', { searchTerm, payload });
      updateBankTransactionMutation.mutate({
        id: searchTerm,
        formData: payload,
      });
    } else {
      //for create
      createBankTransactionMutation.mutate(payload);
    }

    // Reset form after successful submission
    //  }
  };

  const createBankTransactionMutation = useMutation({
    mutationFn: createBankTransaction,
    onSuccess: (data) => {
      showToast('Bank Transaction Created!', 'success');
      if (getPrintSettings('bank_transaction')) {
        if (data?.detail?.pdf_url) {
          window.open(data.detail.pdf_url, '_blank');
        }
      }
      queryClient.invalidateQueries(['bankTransactionListing']);
      handleResetRows();
    },
    onError: (error) => {
      showToast(
        `Error creating Bank Voucher :` + JSON.stringify(error.message),
        'danger'
      );
      console.error('Error creating Bank Voucher', error);
    },
  });

  const updateBankTransactionMutation = useMutation({
    // mutationFn: ({ searchTerm, formData }) => updateBankTransaction(searchTerm, formData),
    mutationFn: ({ id, formData }) => updateBankTransaction(id, formData),
    onSuccess: (data) => {
      showToast('Bank Transaction   Updated!', 'success');
      if (getPrintSettings('bank_transaction')) {
        if (data?.detail?.pdf_url) {
          window.open(data.detail.pdf_url, '_blank');
        }
      }
      queryClient.invalidateQueries(['bankTransaction', searchTerm]);
      handleResetRows();
    },
    onError: (error) => {
      showErrorToast(searchTerm);
      console.error('Error updating Bank Transaction', error);
      showErrorToast(error);
    },
  });

  const handleResetRows = () => {
    setIsDisabled(true);
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
    // Clear saved form values when resetting
    clearFormValues(formId);
  };

  const navigate = useNavigate();

  // Modify the from account options based on transaction type
  const getFromAccountOptions = () => {
    const baseOptions = [
      { label: 'Account Abc', value: 'account_abc' },
      { label: 'Account Xyz', value: 'account_xyz' },
    ];

    if (transactionType === 'inward_tt') {
      if (!ledger) return baseOptions;

      switch (ledger) {
        case 'party':
          return [...baseOptions, { label: 'Add New PL', value: 'add_new_pl' }];
        case 'general':
          return [...baseOptions, { label: 'Add New GL', value: 'add_new_gl' }];
        case 'walkin':
          return [
            ...baseOptions,
            { label: 'Add New WIC', value: 'add_new_wic' },
          ];
        default:
          return baseOptions;
      }
    } else {
      // For deposit or withdrawal, show Add New GL option
      return [...baseOptions, { label: 'Add New GL', value: 'add_new_gl' }];
    }
  };

  // Modify the from account onChange handler
  const handleFromAccountChange = (selected) => {
    if (selected.value === 'add_new_gl') {
      setShowAddLedgerModal('add new gl');
    } else if (selected.value?.includes('add_new')) {
      // Map the selected value to the correct modal type
      const modalTypes = {
        add_new_pl: 'add new pl',
        add_new_wic: 'add new wic',
      };
      setShowAddLedgerModal(modalTypes[selected.value] || '');
    } else {
      setFromAccount(selected.value);
      setErrors({ ...errors, fromAccount: '' });
    }
  };

  // Modify the search term onChange handler
  const handleSearchChange = (e) => {
    const value = e.target.value;

    setSearchTerm(value);

    // Show table when search is empty
    if (!value) {
      setShowDepositTable(true);
      setShowTransactionDetails(false);
      setCurrentTransaction(null);
      setIsDisabled(true);
      setShowRightCards(false);
    } else {
      setShowDepositTable(false);
    }
  };

  const {
    data: transactionDataRes,
    isLoading,
    isFetching,
    isError,
    error,
  } = useQuery({
    queryKey: ['transactionData', headerTransactionTypeValue, searchTerm],
    queryFn: () =>
      getBankTransactionListing({
        search: searchTerm,
        transaction_type: headerTransactionTypeValue,
      }),
    enabled: !!headerTransactionTypeValue,
    staleTime: 1000 * 60 * 5,
  });

  console.log('transactionDataRes', transactionDataRes);

  const activeTransaction = transactionDataRes?.data;

  // if(headerTransactionType === 'inward_tt'){
  //   setInwardData(activeTransaction)
  // }else if(headerTransactionType === 'deposit'){
  //   setDepositData(activeTransaction)
  // }else {
  //   setWithDrawlData(activeTransaction);
  // }
  //

  useEffect(() => {
    if (headerTransactionTypeValue) {
      console.log(
        `${headerTransactionTypeValue.toUpperCase()} Data:`,
        activeTransaction
      );
    }
  }, [headerTransactionTypeValue, activeTransaction]);

  // Modify the search button click handler
  const handleSearchButtonClick = () => {
    if (!searchTerm) {
      setShowDepositTable(true);
      return;
    }

    // Find matching transaction in mock data
    const transaction = currentTransaction; // MOCK_TRANSACTIONS[headerTransactionType]?.['03'];
    if (transaction || showTransactionDetails) {
      setCurrentTransaction(transaction);
      setShowTransactionDetails(true);
      setIsDisabled(false);
      setShowRightCards(true);
    } else {
      setShowTransactionDetails(false);
      setCurrentTransaction(null);
      setIsDisabled(true);
      setShowRightCards(false);
    }
  };

  const getAccountsByTypeMode = (mode) => {
    if (!mode) {
      return [{ label: 'Select Mode', value: null, isDisabled: true }];
    }

    mode = 'bank_cash';

    if (mode === 'bank_cash') {
      const banks = modesData.bank;
      const cash = modesData.cash;

      if (banks.loading || cash.loading) {
        return [{ label: 'Loading...', value: null, isDisabled: true }];
      }

      if (banks.error || cash.error) {
        console.error(
          'Unable to fetch Bank/Cash Mode',
          banks.errorMessage || cash.errorMessage
        );
        return [{ label: 'Unable to fetch Bank/Cash Mode', value: null }];
      }

      const merged = [...(banks.data || []), ...(cash.data || [])];

      console.log('merged accounts', merged);

      return merged.map((x) => ({
        value: x?.id,
        label: x?.account_name,
      }));
    }

    const { data, loading, error, errorMessage } = modesData[mode] || {};

    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (error) {
      console.error('Unable to fetch Mode', errorMessage);
      return [{ label: 'Unable to fetch Mode', value: null }];
    }

    return (
      data?.map((x) => ({
        value: x?.id,
        label: x?.account_name,
      })) || []
    );
  };

  const getAccountsByTypeOptions = (accountType) => {
    console.log(accountType);

    if (!accountType) {
      return [{ label: 'Select Ledger', value: null, isDisabled: true }];
    }

    const { data, loading, error, errorMessage } =
      accountData[accountType] || {};

    console.log(accountData[accountType]);

    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (error) {
      console.error('Unable to fetch Accounts', errorMessage);
      return [{ label: 'Unable to fetch Accounts', value: null }];
    }
    let options =
      data?.map((x) => ({
        value: x?.id,
        label: x?.title,
      })) || [];
    switch (accountType) {
      case 'party':
        options.push({
          label: `Add New PL`,
          value: null,
        });
        break;
      case 'general':
        options.push({
          label: `Add New GL`,
          value: null,
        });
        break;
      case 'walkin':
        options.push({
          label: `Add New WIC`,
          value: null,
        });
        break;
      default:
        break;
    }
    return options;
  };

  // Get account options //
  const {
    data: partyAccounts,
    isLoading: isLoadingParty,
    isError: isErrorParty,
    error: errorParty,
  } = useQuery({
    queryKey: ['accounts', 'party'],
    queryFn: () => getAccountsbyType('party'),
    staleTime: 1000 * 60 * 5,
  });

  const {
    data: generalAccounts,
    isLoading: isLoadingGeneral,
    isError: isErrorGeneral,
    error: errorGeneral,
  } = useQuery({
    queryKey: ['accounts', 'general'],
    queryFn: () => getAccountsbyType('general'),
    staleTime: 1000 * 60 * 5,
  });

  const {
    data: walkinAccounts,
    isLoading: isLoadingWalkin,
    isError: isErrorWalkin,
    error: errorWalkin,
  } = useQuery({
    queryKey: ['accounts', 'walkin'],
    queryFn: () => getAccountsbyType('walkin'),
    staleTime: 1000 * 60 * 5,
  });

  const accountData = {
    party: {
      data: partyAccounts,
      loading: isLoadingParty,
      error: isErrorParty,
      errorMessage: errorParty,
    },
    general: {
      data: generalAccounts,
      loading: isLoadingGeneral,
      error: isErrorGeneral,
      errorMessage: errorGeneral,
    },
    walkin: {
      data: walkinAccounts,
      loading: isLoadingWalkin,
      error: isErrorWalkin,
      errorMessage: errorWalkin,
    },
  };

  // get modes

  const {
    data: modeBank,
    isLoading: isLoadingBank,
    isError: isErrorBank,
    error: errorBank,
  } = useQuery({
    queryKey: ['type', 'Bank'],
    queryFn: () => getPaymentVoucherMode('Bank'),
    staleTime: 1000 * 60 * 5,
  });

  const {
    data: modeCash,
    isLoading: isLoadingCash,
    isError: isErrorCash,
    error: errorCash,
  } = useQuery({
    queryKey: ['type', 'Cash'],
    queryFn: () => getPaymentVoucherMode('Cash'),
    staleTime: 1000 * 60 * 5,
  });

  const {
    data: modePdc,
    isLoading: isLoadingPdc,
    isError: isErrorPdc,
    error: errorPdc,
  } = useQuery({
    queryKey: ['type', 'Pdc'],
    queryFn: () => getPaymentVoucherMode('Pdc'),
    staleTime: 1000 * 60 * 5,
  });

  const {
    data: modeOnline,
    isLoading: isLoadingOnline,
    isError: isErrorOnline,
    error: errorOnline,
  } = useQuery({
    queryKey: ['type', 'Online'],
    queryFn: () => getPaymentVoucherMode('Online'),
    staleTime: 1000 * 60 * 5,
  });

  const modesData = {
    bank: {
      data: modeBank,
      loading: isLoadingBank,
      error: isErrorBank,
      errorMessage: errorBank,
    },
    cash: {
      data: modeCash,
      loading: isLoadingBank,
      error: isErrorBank,
      errorMessage: errorBank,
    },
    pdc: {
      data: modePdc,
      loading: isLoadingPdc,
      error: isErrorPdc,
      errorMessage: errorPdc,
    },
    online: {
      data: modeOnline,
      loading: isLoadingOnline,
      error: isErrorOnline,
      errorMessage: errorOnline,
    },
  };

  const {
    data: modeCheques,
    isLoading: isLoadingCheques,
    isError: isErrorCheques,
    error: errorCheques,
  } = useQuery({
    queryKey: ['bank_id', selectedBank],
    queryFn: () => getBankTransactionChequeNumberByBank(selectedBank),
    staleTime: 1000 * 60 * 5,
    enabled: !!selectedBank,
  });

  const chequeOptions =
    modeCheques?.map((cheque) => ({
      label: cheque.cheque_number, // adjust this based on your API response
      value: cheque.id,
    })) || [];

  useEffect(() => {
    modeCheques;
  }, [modeCheques]);

  const handlePrint = () => {
    if (currentTransaction?.pdf_url) {
      window.open(currentTransaction?.pdf_url, '_blank');
    }
  };

  // Modify the form content based on transaction type
  const renderFormContent = ({
    values,
    errors,
    touched,
    handleChange,
    onChange,
    handleBlur,
    setFieldValue,
  }) => {
    useEffect(() => {
      const amount = parseFloat(values.amount);
      const percentage = parseFloat(values.commissionPercentage);

      if (!isNaN(amount) && !isNaN(percentage)) {
        const commission = (amount * percentage) / 100;
        setFieldValue('commissionAmount', commission.toFixed(2));
      } else {
        setFieldValue('commissionAmount', '');
      }
    }, [values.amount, values.commissionPercentage, setFieldValue]);

    if (values.transactionType === 'inward_tt') {
      return (
        <div>
          {/* Transaction Type and Bank */}
          <div className="row">
            <div className="col-12 col-sm-6 mb-45">
              <SearchableSelect
                label="Transaction Type"
                name="transactionType"
                options={[
                  { label: 'Deposit', value: 'deposit' },
                  { label: 'Inward TT', value: 'inward_tt' },
                  { label: 'Withdrawal', value: 'withdrawal' },
                ]}
                value={values.transactionType}
                onChange={(selected) => {
                  setFieldValue('transactionType', selected.value);
                  setHeaderTransactionType(selected.value);
                }}
                placeholder="Select Transaction Type"
                isDisabled={isDisabled}
                error={touched.transactionType && errors.transactionType}
              />
            </div>
            <div className="col-12 col-sm-6 mb-45">
              <SearchableSelect
                label="Bank"
                name="bank"
                options={banksList}
                // options={[
                //   { label: 'Bank A', value: 'bank_a' },
                //   { label: 'Bank B', value: 'bank_b' },
                // ]}
                value={values.bank}
                onChange={(selected) => setFieldValue('bank', selected.value)}
                placeholder="Select Bank"
                isDisabled={isDisabled}
                error={touched.bank && errors.bank}
              />
            </div>
            <div className="col-12 col-sm-6 mb-45">
              <div className="combined-select-container">
                <label>From Account</label>
                <div
                  className={`combined-select-input ${
                    isDisabled ? 'disabled-combined-select' : ''
                  }`}
                >
                  <div className="combined-select-left">
                    <SearchableSelect
                      name="ledger"
                      options={[
                        { label: 'PL', value: 'party' },
                        { label: 'GL', value: 'general' },
                        { label: 'WIC', value: 'walkin' },
                      ]}
                      className="ledger-select__control"
                      value={values.ledger}
                      onChange={(selected) =>
                        setFieldValue('ledger', selected.value)
                      }
                      placeholder="Ledger"
                      isDisabled={isDisabled}
                      error={touched.ledger && errors.ledger}
                    />
                  </div>
                  <div className="separator-between-selects">|</div>
                  <div className="combined-select-right">
                    <SearchableSelect
                      name="fromAccount"
                      className="ledger-select__control"
                      //  options={getFromAccountOptions(values.ledger)}
                      options={getAccountsByTypeOptions(values.ledger)}
                      value={values.fromAccount}
                      onChange={(selected) => {
                        // alert(JSON.stringify(selected))
                        // if (selected.value?.includes('add_new')) {
                        //   setShowAddLedgerModal(
                        //     selected.value.replace('add_new_', 'add new ')
                        //   );
                        // } else {

                        //   setFieldValue('fromAccount', selected.value);
                        // }
                        setFieldValue('fromAccount', selected.value);
                        setFromAccount(selected.value);
                        setSelectedBank(selected.value);
                      }}
                      placeholder="Select From Account"
                      isDisabled={isDisabled || !values.ledger}
                      error={touched.fromAccount && errors.fromAccount}
                    />
                  </div>
                </div>
              </div>
            </div>
            {/* <div className="col-12 col-sm-6 mb-45">
              <CustomInput
                label="Cheque Number"
                name="chequeNumber"
                type="text"
                placeholder="Enter Cheque Number"
                value={values.chequeNumber}
                style={{ marginBottom: 0 }}
                onChange={handleChange}
                onBlur={handleBlur}
                disabled={isDisabled}
                error={touched.chequeNumber && errors.chequeNumber}
              />
            </div> */}
            <div className="col-12 col-sm-6 mb-45">
              <div className="combined-select-container">
                <label>Amount</label>
                <div
                  className={`combined-select-input ${
                    isDisabled ? 'disabled-combined-select' : ''
                  }`}
                >
                  <div className="combined-select-left mt-1">
                    <SearchableSelect
                      name={'currency'}
                      options={currencyOptions}
                      className="ledger-select__control"
                      isDisabled={isDisabled}
                      placeholder={'Currency'}
                      value={values.currency}
                      onChange={(selected) => {
                        setFieldValue('currency', selected.value);
                      }}
                      onBlur={handleBlur}
                    />

                    {/* <SearchableSelect
                      name="currency"
                      options={[
                        { label: 'DHS', value: 'DHS' },
                        { label: 'BTC', value: 'BTC' },
                        { label: 'CAD', value: 'CAD' },
                        { label: 'ETH', value: 'ETH' },
                        { label: 'EUR', value: 'EUR' },
                        { label: 'GBP', value: 'GBP' },
                        { label: 'PKR', value: 'PKR' },
                        { label: 'INR', value: 'INR' },
                      ]}
                      value={values.currency}
                      onChange={(selected) =>
                        setFieldValue('currency', selected.value)
                      }
                      isDisabled={isDisabled}
                      style={{ width: '100px' }}
                      className="ledger-select__control"
                      error={touched.currency && errors.currency}
                    /> */}
                  </div>
                  <div className="separator-between-selects">|</div>
                  <div className="combined-select-right mt-3">
                    <CustomInput
                      name="amount"
                      type="number"
                      inputClass="ledger-select__control"
                      placeholder="Enter Amount"
                      value={values.amount}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      disabled={isDisabled}
                      error={touched.amount && errors.amount}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="col-12 col-sm-6 mb-45">
              <SearchableSelect
                label="Commission Type"
                name="commissionType"
                options={[
                  { label: 'Commission Income', value: 'income' },
                  { label: 'Commission Expense', value: 'expense' },
                ]}
                value={values.commissionType}
                onChange={(selected) =>
                  setFieldValue('commissionType', selected.value)
                }
                placeholder="Commission Income"
                isDisabled={isDisabled}
                error={touched.commissionType && errors.commissionType}
              />
            </div>
            <div className="col-12 col-sm-6 mb-45">
              <CustomInput
                label="Commission %"
                name="commissionPercentage"
                type="number"
                placeholder="Enter commission %"
                value={values.commissionPercentage}
                onChange={handleChange}
                onBlur={handleBlur}
                disabled={isDisabled}
                error={
                  touched.commissionPercentage && errors.commissionPercentage
                }
                style={{ marginBottom: 0 }}
              />
            </div>
            <div className="col-12 col-sm-6 mb-45">
              <CustomInput
                label="Commission"
                name="commissionAmount"
                type="number"
                value={values.commissionAmount}
                onChange={handleChange}
                onBlur={handleBlur}
                disabled={true}
                error={touched.commissionAmount && errors.commissionAmount}
                style={{ marginBottom: 0 }}
              />
            </div>
            <div className="col-12 col-sm-6 mb-45">
              <SearchableSelect
                label="Country"
                name="country"
                options={countryOptions}
                // options={[
                //   { label: 'UAE', value: '2' },
                //   { label: 'USA', value: '1' },
                // ]}
                value={values.country}
                onChange={(selected) =>
                  setFieldValue('country', selected.value)
                }
                placeholder="Select Country"
                isDisabled={isDisabled}
                error={touched.country && errors.country}
              />
            </div>
          </div>
          {/* Narration */}
          <div>
            <label>Narration</label>
            <CustomInput
              name="narration"
              type="textarea"
              placeholder="Enter Narration"
              value={values.narration}
              onChange={handleChange}
              onBlur={handleBlur}
              disabled={isDisabled}
              rows={5}
              style={{ height: '100px' }}
              error={touched.narration && errors.narration}
            />
          </div>
        </div>
      );
    }

    // Regular deposit/withdrawal form
    return (
      <div>
        {/* Transaction Type and Cheque Number */}
        <div className="row">
          <div className="col-12 col-sm-6 mb-45">
            <SearchableSelect
              label="Transaction Type"
              name="transactionType"
              options={[
                { label: 'Deposit', value: 'deposit' },
                { label: 'Inward TT', value: 'inward_tt' },
                { label: 'Withdrawal', value: 'withdrawal' },
              ]}
              value={values.transactionType}
              onChange={(selected) => {
                setFieldValue('transactionType', selected.value);
                setHeaderTransactionType(selected.value);
              }}
              placeholder="Select Transaction Type"
              isDisabled={isDisabled}
              error={touched.transactionType && errors.transactionType}
            />
          </div>
          <div className="col-12 col-sm-6 mb-45">
            <SearchableSelect
              label="Cheque Number"
              name="chequeNumber"
              // options={[
              //   { label: '0000001', value: '0000001' },
              //   { label: '0000002', value: '0000002' },
              // ]}
              options={chequeOptions}
              value={values.chequeNumber}
              onChange={(selected) =>
                setFieldValue('chequeNumber', selected.value)
              }
              placeholder="Select Cheque Number"
              isDisabled={isDisabled}
              error={touched.chequeNumber && errors.chequeNumber}
            />
          </div>
          <div className="col-12 mb-45">
            <div className="d-flex gap-3 flex-md-nowrap flex-wrap">
              <div style={{ width: '100%' }}>
                {/*<SearchableSelect*/}
                {/*  label="From Account"*/}
                {/*  name="fromAccount"*/}
                {/*  // options={getFromAccountOptions()}*/}
                {/*  options={getAccountsByTypeMode('bank_cash')}*/}
                {/*  // options={getAccountsByTypeOptions('party')}*/}
                {/*  value={values.fromAccount}*/}
                {/*  onChange={(selected) => {*/}
                {/*    if (selected.value === 'add_new_gl') {*/}
                {/*      setShowAddLedgerModal('add new gl');*/}
                {/*    } else {*/}
                {/*      setFieldValue('fromAccount', selected.value);*/}
                {/*      setSelectedBank(selected.value);*/}
                {/*      setFromAccount(selected.value);*/}
                {/*    }*/}
                {/*  }}*/}
                {/*  placeholder="Select From Account"*/}
                {/*  isDisabled={isDisabled}*/}
                {/*  error={touched.fromAccount && errors.fromAccount}*/}
                {/*/>*/}

                <SearchableSelect
                  name="fromAccount"
                  label="From Account"
                  //  options={getFromAccountOptions(values.ledger)}
                  options={getAccountsByTypeMode('bank_cash')}
                  value={values.fromAccount}
                  onChange={(selected) => {
                    // alert(JSON.stringify(selected))
                    // if (selected.value?.includes('add_new')) {
                    //   setShowAddLedgerModal(
                    //     selected.value.replace('add_new_', 'add new ')
                    //   );
                    // } else {

                    //   setFieldValue('fromAccount', selected.value);
                    // }
                    setFieldValue('fromAccount', selected.value);
                    setFromAccount(selected.value);
                    setSelectedBank(selected.value);
                  }}
                  placeholder="Select From Account"
                  isDisabled={isDisabled}
                  error={touched.fromAccount && errors.fromAccount}
                />
              </div>
              <div style={{ width: '100%' }}>
                <SearchableSelect
                  label="To Account"
                  name="toAccount"
                  // options={[{ label: 'Account Abc', value: 'account_abc' }]}
                  options={getAccountsByTypeMode('bank_cash')}
                  value={values.toAccount}
                  onChange={(selected) =>
                    setFieldValue('toAccount', selected.value)
                  }
                  placeholder="Select To Account"
                  isDisabled={isDisabled}
                  error={touched.toAccount && errors.toAccount}
                />
              </div>
              <div className="flex-shrink-0 d-flex align-items-end">
                <CustomButton
                  text="Switch Account"
                  onClick={() => {
                    const tempFrom = values.fromAccount;
                    setFieldValue('fromAccount', values.toAccount);
                    setFieldValue('toAccount', tempFrom);

                    setFromAccount(values.toAccount);
                  }}
                  disabled={isDisabled}
                />
              </div>
            </div>
          </div>
          <div className="col-12 col-sm-6 mb-45">
            <div className="combined-select-container">
              <label>Amount</label>
              <div
                className={`combined-select-input ${
                  isDisabled ? 'disabled-combined-select' : ''
                }`}
              >
                <div className="combined-select-left mt-1">
                  <SearchableSelect
                    name={'currency'}
                    options={currencyOptions}
                    style={{ width: '100px' }}
                    className="ledger-select__control"
                    isDisabled={isDisabled}
                    placeholder={'Currency'}
                    value={values.currency}
                    onChange={(selected) => {
                      setFieldValue('currency', selected.value);
                    }}
                    onBlur={handleBlur}
                  />

                  {/* <SearchableSelect
                    name="currency"
                    options={[
                      { label: 'DHS', value: 'DHS' },
                      { label: 'BTC', value: 'BTC' },
                      { label: 'CAD', value: 'CAD' },
                      { label: 'ETH', value: 'ETH' },
                      { label: 'EUR', value: 'EUR' },
                      { label: 'GBP', value: 'GBP' },
                      { label: 'PKR', value: 'PKR' },
                      { label: 'INR', value: 'INR' },
                    ]}
                    value={values.currency}
                    onChange={(selected) => {
                      if (selected.label?.toLowerCase()?.startsWith('eth')) {
                        setShowMissingCurrencyRateModal(true);
                      } else {
                        setFieldValue('currency', selected.value);
                      }
                    }}
                    isDisabled={isDisabled}
                    style={{ width: '100px' }}
                    className="ledger-select__control"
                    error={touched.currency && errors.currency}
                  /> */}
                </div>
                <div className="separator-between-selects">|</div>
                <div className="combined-select-right mt-3">
                  <CustomInput
                    name="amount"
                    type="number"
                    inputClass="ledger-select__control"
                    placeholder="Enter Amount"
                    value={values.amount}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    disabled={isDisabled}
                    error={touched.amount && errors.amount}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* From/To Account */}

        {/* Amount */}

        {/* Narration */}
        <div>
          <label>Narration</label>
          <CustomInput
            name="narration"
            type="textarea"
            placeholder="Enter Narration"
            rows={5}
            value={values.narration}
            onChange={handleChange}
            onBlur={handleBlur}
            disabled={isDisabled}
            style={{ height: '100px' }}
            error={touched.narration && errors.narration}
          />
        </div>
      </div>
    );
  };

  // Modify the populateFormData function
  const populateFormData = (transaction) => {
    // Set initial form values
    const formValues = {
      transactionType: transaction.transactionType.toLowerCase(),
      chequeNumber: transaction.chequeNumber,
      fromAccount: transaction.fromAccount,
      toAccount: transaction.toAccount,
      amount: transaction.amount,
      currency: transaction.currency,
      narration: transaction.narration,
      date: transaction.date || new Date().toISOString().split('T')[0],
    };

    // Add inward TT specific fields if applicable
    if (transaction.transactionType === 'Inward TT') {
      formValues.bank = transaction.bank;
      formValues.commissionType = transaction.commissionType;
      formValues.commissionPercentage = transaction.commissionPercentage;
      formValues.commissionAmount = transaction.commissionAmount;
      formValues.country = transaction.country;
      formValues.ledger = transaction.ledger;
    }

    // Update all form fields
    Object.entries(formValues).forEach(([key, value]) => {
      if (value !== undefined) {
        setFieldValue(key, value);
      }
    });

    // Update header transaction type
    setHeaderTransactionType(transaction.transactionType.toLowerCase());

    // Enable form editing
    setIsDisabled(false);
    setShowTransactionDetails(false);
    setShowRightCards(true);
  };

  const handleDelete = (item) => {
    showModal(
      'Delete',
      `Are you sure you want to delete ${getLastNumberText(
        headerTransactionType
      )}?`,
      () => {
        console.log('delete log', item);
        // Close the first modal before showing the second one
        closeModal();

        if (item) {
          var type = item.transactionType.toLowerCase();

          deleteBankTransactionMutation.mutate({
            id: item.id,
            transactionType: type,
          });
        }
        // Show success modal after a small delay to ensure smooth transition
        setTimeout(() => {
          showModal(
            'Deleted',
            `Deleted ${getLastNumberText(headerTransactionType)} successfully`,
            false,
            'success'
          );
        }, 100);
      }
    );
  };

  // Mutation for delete
  const deleteBankTransactionMutation = useMutation({
    mutationFn: ({ id, transactionType }) =>
      deleteBankTransaction(id, transactionType),
    onSuccess: () => {
      showToast('Bank Transaction deleted successfully!', 'success');
      queryClient.invalidateQueries(['bankTransaction', searchTerm]);

      setSearchTerm('');
      setShowDepositTable(true);

      // setDate(new Date().toISOString().split('T')[0]);
    },
    onError: (error) => {
      showErrorToast(error);
    },
  });

  const [formattedTransaction, setFormattedTransaction] = useState(null);

  useEffect(() => {
    if (formikRef.current && formattedTransaction) {
      console.log('Setting values in useEffect:', formattedTransaction);
      formikRef.current.setValues(formattedTransaction);
      setFormattedTransaction(null); // Reset after applying
    }
  }, [formikRef.current, formattedTransaction]);

  // Update TransactionDetails component
  const TransactionDetails = () => {
    console.log('currentTransaction', transactionDetail);
    // if (!currentTransaction) return null;
    if (!transactionDetail) return null;

    const handleEdit = () => {
      console.log('Current Transaction:', transactionDetail); // Debug log

      // Format the transaction data
      const formatted = {
        transactionType: headerTransactionType,
        chequeNumber: parseInt(transactionDetail.cheque_number) || '',
        fromAccount: transactionDetail.from_account_id || '',
        toAccount: transactionDetail.to_account_id || '',
        amount: transactionDetail.amount || '',
        currency: transactionDetail.currency_id, //parseInt(transactionDetail.currency?.id),
        narration: transactionDetail.narration || '',
        date: transactionDetail.date || new Date().toISOString().split('T')[0],
        bank: transactionDetail.bank_id || '',
        commissionType: transactionDetail.commission_type || '',
        commissionPercentage: transactionDetail.commission_percentage || '',
        commissionAmount: transactionDetail.commission_value || '',
        country: transactionDetail.country_id || '',
        ledger: transactionDetail.ledger || '',
      };

      console.log('Formatted Transactionss:', formatted); // Debug log

      // Update all state variables
      setTransactionType(formatted.transactionType);
      setChequeNumber(formatted.chequeNumber);
      setFromAccount(formatted.fromAccount);
      setToAccount(formatted.toAccount);
      setAmount(formatted.amount);
      setCurrency(formatted.currency);
      setNarration(formatted.narration);
      setDate(formatted.date);

      if (headerTransactionType === 'inward_tt') {
        setBank(formatted.bank);
        setCommissionType(formatted.commissionType);
        setCommissionPercentage(formatted.commissionPercentage);
        setCommissionAmount(formatted.commissionAmount);
        setCountry(formatted.country);
        setLedger(formatted.ledger);
      }

      // Reset Formik form
      setFormattedTransaction(formatted); // ✅ triggers useEffect to apply

      // if (formikRef.current) {
      //   console.log('Setting Formik values:', formatted ); // Debug log
      //   formikRef.current.setValues(formatted );
      // }

      // Update UI state
      setIsDisabled(false);
      setShowTransactionDetails(false);
      setShowRightCards(true);
    };

    if (headerTransactionType === 'inward_tt') {
      return (
        <div className="transaction-details">
          <div className="row mb-4">
            <div className="col-md-6">
              <div className="mb-3">
                <label className="text-muted">Transaction Type</label>
                <p className="mb-0">{currentTransaction.transactionType}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">Bank</label>
                <p className="mb-0">{currentTransaction.bank}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">From Account</label>
                <p className="mb-0">{currentTransaction.fromAccount}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">Amount</label>
                <p className="mb-0">
                  {currentTransaction.currency} {currentTransaction.amount}
                </p>
              </div>
              <div className="mb-3">
                <label className="text-muted">Commission %</label>
                <p className="mb-0">
                  {currentTransaction.commissionPercentage}
                </p>
              </div>
            </div>
            <div className="col-md-6">
              <div className="mb-3">
                <label className="text-muted">Cheque Number</label>
                <p className="mb-0">{currentTransaction.chequeNumber}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">Commission Type</label>
                <p className="mb-0">{currentTransaction.commissionType}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">Commission in DHS</label>
                <p className="mb-0">{currentTransaction.commissionAmount}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">Country</label>
                <p className="mb-0">{currentTransaction.country}</p>
              </div>
            </div>
          </div>
          <div className="mb-4">
            <label className="text-muted">Narratsssion</label>
            <p className="mb-0">{currentTransaction.narration}</p>
          </div>

          {/* <div className="d-flex justify-content-between align-items-center mt-4">
            <div className="d-flex gap-2">
              <CustomButton
                text="Edit"
                variant="warning"
                onClick={handleEdit}
              />
              <CustomButton
                text="Delete"
                onClick={() => handleDelete(currentTransaction)}
                className={'secondaryButton'}
              />
              <CustomButton text="Print" className={'secondaryButton'} />
            </div>
            <div className="d-flex gap-3 align-items-center">
              <FaChevronLeft size={20} style={{ cursor: 'pointer' }} />
              <FaChevronRight size={20} style={{ cursor: 'pointer' }} />
              <FaPaperclip
                size={20}
                style={{ cursor: 'pointer' }}
                uploadAttachmentsModal
              />
            </div>
          </div>
          <div className="d-flex justify-content-between align-items-center mt-4">
            <div>Last Internal Payment Voucher Number: 05</div>
          </div>*/}
        </div>
      );
    }

    return (
      <>
        <div className="transaction-details">
          <div className="row mb-4">
            <div className="col-md-6">
              <div className="mb-3">
                <label className="text-muted">Transaction Type</label>
                <p className="mb-0">{currentTransaction.transactionType}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">From Account</label>
                <p className="mb-0">{currentTransaction.fromAccount}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">Currency</label>
                <p className="mb-0">
                  {currentTransaction.currency} {currentTransaction.amount}
                </p>
              </div>
            </div>
            <div className="col-md-6">
              <div className="mb-3">
                <label className="text-muted">Cheque Number</label>
                <p className="mb-0">{currentTransaction.chequeNumber}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">To Account</label>
                <p className="mb-0">{currentTransaction.toAccount}</p>
              </div>
            </div>
          </div>
          <div className="mb-4">
            <label className="text-muted">Narration</label>
            <p className="mb-0">{currentTransaction.narration}</p>
          </div>

          <div className="d-flex justify-content-between align-items-center mt-4">
            <div className="d-flex gap-2">
              <CustomButton
                text="Edit"
                variant="warning"
                onClick={handleEdit}
              />
              <CustomButton
                text="Delete"
                onClick={() => handleDelete(currentTransaction)}
                className={'secondaryButton'}
              />
              <CustomButton
                text="Print"
                onClick={handlePrint}
                className={'secondaryButton'}
              />
            </div>
            <div className="d-flex gap-3 align-items-center">
              <FaChevronLeft size={20} style={{ cursor: 'pointer' }} />
              <FaChevronRight size={20} style={{ cursor: 'pointer' }} />
              <FaPaperclip
                size={20}
                style={{ cursor: 'pointer' }}
                uploadAttachmentsModal
              />
            </div>
          </div>
          {/*  <div className="d-flex justify-content-between align-items-center mt-4">*/}
          {/*  <div>Last BDV Number: 05</div>*/}
          {/*</div>  */}
        </div>
        {/*<VoucherNavigationBar*/}
        {/*  actionButtons={[*/}
        {/*    {*/}
        {/*      text: 'Edit', onClick: () => {*/}
        {/*      },*/}
        {/*    },*/}
        {/*    {*/}
        {/*      text: 'Delete', onClick: () => {*/}
        {/*      },*/}
        {/*    },*/}
        {/*    {*/}
        {/*      text: 'Print', onClick: () => {*/}
        {/*      },*/}
        {/*    },*/}
        {/*  ]}*/}
        {/*  lastVoucherHeading="Last BDV Number"*/}
        {/*/>*/}
      </>
    );
  };

  const renderAddLedgerForm = () => {
    switch (showAddLedgerModal) {
      case 'add new pl':
        return (
          <PartyLedgerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new wic':
        return (
          <WalkInCustomerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new gl':
        return (
          <ChartOfAccountForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      default:
        break;
    }
  };

  return (
    <>
      <section className="position-relative">
        <div className="d-flex gap-3 justify-content-between flex-wrap">
          <div className="">
            {(showDepositTable || showTransactionDetails || !isDisabled) && (
              <BackButton
                handleBack={() => {
                  setIsDisabled(true);
                  setShowRightCards(false);
                  setShowTransactionDetails(false);
                  setShowDepositTable(false);
                }}
              />
            )}
            <h2 className="screen-title mb-0">Bank Transactions</h2>
          </div>
          {isDisabled && !showDepositTable && (
            <CustomButton text={'New'} onClick={handleNewClick} />
          )}
        </div>
        <div className="d-flex justify-content-between align-items-end gap-2 flex-wrap mb-2">
          <div className="combined-select-container">
            <div className={`combined-select-input`}>
              <div className="combined-select-left mt-1">
                <SearchableSelect
                  options={[
                    { label: 'Deposit', value: 'deposit' },
                    { label: 'Inward TT', value: 'inward_tt' },
                    { label: 'Withdrawal', value: 'withdrawal' },
                  ]}
                  value={headerTransactionType}
                  className="ledger-select__control"
                  onChange={(selected) =>
                    setHeaderTransactionType(selected.value)
                  }
                  borderRadius={10}
                />
              </div>
              <div className="separator-between-selects">|</div>
              <div className="combined-select-right mt-3">
                <CustomInput
                  type="text"
                  inputClass="ledger-select__control"
                  placeholder={getSearchPlaceholder(headerTransactionType)}
                  value={searchTerm}
                  onChange={handleSearchChange}
                  rightIcon={FaMagnifyingGlass}
                  borderRadius={10}
                  onButtonClick={handleSearchButtonClick}
                />
              </div>
            </div>
          </div>
          <div>
            <CustomInput
              label="Date"
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              borderRadius={10}
            />
          </div>
        </div>
        <div className="d-card">
          {showDepositTable ? (
            <CustomTable
              hasFilters={false}
              setFilters={false}
              headers={
                headerTransactionType === 'withdrawal'
                  ? withdrawalTableHeaders
                  : headerTransactionType === 'inward_tt'
                  ? inwardTTTableHeaders
                  : depositTableHeaders
              }
              pagination={pagination}
              updatePagination={updatePagination}
              isLoading={false}
              sortKey={false}
              sortOrder={false}
              handleSort={false}
              // isPaginated={false}
            >
              <tbody>
                {/*{(headerTransactionType === 'withdrawal'*/}
                {/*    ? MOCK_WITHDRAWAL_DATA*/}
                {/*    : headerTransactionType === 'inward_tt'*/}
                {/*      ? MOCK_INWARD_TT_DATA*/}
                {/*      : MOCK_DEPOSIT_DATA*/}
                {/*).map((item) => (*/}
                {activeTransaction?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.date}</td>
                    <td
                      // onClick={() => {
                      //   setShowDepositTable(false);
                      //   setShowTransactionDetails(true);
                      //   const transaction =
                      //     // MOCK_TRANSACTIONS[headerTransactionType]?.['03'];..
                      //   if (transaction) {
                      //     setCurrentTransaction(transaction);
                      //     setShowTransactionDetails(true);
                      //     setIsDisabled(false);
                      //     setShowRightCards(true);
                      //   }
                      // }}
                      onClick={() => {
                        setShowDepositTable(false);
                        setShowTransactionDetails(true);

                        setTransactionDetail(item);

                        const transaction = {
                          id: item?.voucher?.voucher_no,
                          transactionType: item.transaction_type?.toUpperCase(), // e.g., 'DEPOSIT'
                          chequeNumber: item.cheque?.cheque_number || 'N/A',
                          fromAccount:
                            item.from_account_details?.title || 'N/A',
                          toAccount: item.to_account_details?.title || 'N/A',
                          currency: item.currency?.currency_code || 'N/A',
                          amount: Number(item.amount).toLocaleString(), // 500 => "500"
                          narration: item.narration || 'No narration',
                          commissionType: item.commission_type || undefined,
                          commissionPercentage:
                            item.commission_percentage || undefined,
                          commissionAmount: item.commission_value || undefined,
                          country:
                            item.country?.name || item.country_id || undefined, // if country is object
                          pdf_url: item?.pdf_url,
                          voucher_no: item?.voucher?.voucher_no,
                        };

                        setCurrentTransaction(transaction);
                        setShowRightCards(true);
                        setIsDisabled(false);

                        setSearchTerm(item?.voucher?.voucher_no);

                        setPageState('edit');
                      }}
                    >
                      <p className="text-link text-decoration-underline cp mb-0">
                        {headerTransactionType === 'withdrawal'
                          ? item.voucher?.voucher_no
                          : headerTransactionType === 'inward_tt'
                          ? item.voucher?.voucher_no
                          : item.voucher?.voucher_no}
                      </p>
                    </td>
                    {headerTransactionType === 'inward_tt' && (
                      <>
                        <td>{item.bank}</td>
                        <td>{item.ledger}</td>
                      </>
                    )}
                    <td>{item.from_account_details?.title}</td>
                    <td>{item.to_account_details?.title}</td>
                    <td>{item.currency?.currency_code}</td>
                    <td>{item.fc_net_total}</td>
                    <td>{item.lc_net_total}</td>
                    {headerTransactionType === 'inward_tt' && (
                      <td
                        style={{
                          color:
                            item.fc_commission === '100'
                              ? '#EF4444'
                              : '#22C55E',
                        }}
                      >
                        {item.fc_commission}
                      </td>
                    )}
                    <td>{item.creator?.user_id}</td>
                    <td>
                      {formatDate(item.created_at, 'DD/MM/YYYY - HH:MM:SS')}
                    </td>
                    <td>{/*{item.has_attachment}*/}</td>
                  </tr>
                ))}
              </tbody>
            </CustomTable>
          ) : // Original form view
          showTransactionDetails ? (
            <TransactionDetails />
          ) : (
            <Formik
              innerRef={formikRef}
              initialValues={initialValues}
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
            >
              {(formikProps) => (
                <Form>
                  <div className="row justify-content-between">
                    <div className="col-12 col-xxl-9">
                      {renderFormContent(formikProps)}
                    </div>
                    <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
                      {showRightCards && (
                        <div>
                          {/* Account Balance Cards */}
                          <div>
                            {/* Current Account */}
                            <div>
                              <h6 className="mb-2">Account Balance</h6>
                              <div className="d-card mb-4 account-balance-card">
                                <div className="mb-3 account-name w-100">
                                  {MOCK_CURRENT_ACCOUNT.name}
                                </div>
                                <table className="w-100">
                                  <thead>
                                    <tr
                                      style={{
                                        borderBottom: '1px solid #E5E7EB',
                                      }}
                                    >
                                      <th
                                        style={{
                                          padding: '8px 0',
                                          color: '#6B7280',
                                          fontWeight: '500',
                                        }}
                                      >
                                        FCy
                                      </th>
                                      <th
                                        style={{
                                          padding: '8px 0',
                                          color: '#6B7280',
                                          fontWeight: '500',
                                        }}
                                      >
                                        Balance
                                      </th>
                                      <th
                                        style={{
                                          padding: '8px 0',
                                          color: '#6B7280',
                                          fontWeight: '500',
                                        }}
                                      ></th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {MOCK_CURRENT_ACCOUNT.balances.map(
                                      (balance, index) => (
                                        <tr key={index}>
                                          <td
                                            style={{
                                              padding: '8px 0',
                                              color: balance.color,
                                              fontWeight: '500',
                                            }}
                                          >
                                            {balance.currency}
                                          </td>
                                          <td style={{ padding: '8px 0' }}>
                                            {balance.amount}
                                          </td>
                                          <td style={{ padding: '8px 0' }}>
                                            {balance.type}
                                          </td>
                                        </tr>
                                      )
                                    )}
                                  </tbody>
                                </table>
                              </div>
                            </div>

                            {/* Savings Account */}
                            <div>
                              <h6 className="mb-2">Account Balance</h6>
                              <div className="d-card mb-4 account-balance-card">
                                <div className="mb-3 account-name w-100">
                                  {MOCK_SAVINGS_ACCOUNT.name}
                                </div>
                                <table className="w-100">
                                  <thead>
                                    <tr
                                      style={{
                                        borderBottom: '1px solid #E5E7EB',
                                      }}
                                    >
                                      <th
                                        style={{
                                          padding: '8px 0',
                                          color: '#6B7280',
                                          fontWeight: '500',
                                        }}
                                      >
                                        FCy
                                      </th>
                                      <th
                                        style={{
                                          padding: '8px 0',
                                          color: '#6B7280',
                                          fontWeight: '500',
                                        }}
                                      >
                                        Balance
                                      </th>
                                      <th
                                        style={{
                                          padding: '8px 0',
                                          color: '#6B7280',
                                          fontWeight: '500',
                                        }}
                                      ></th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {MOCK_SAVINGS_ACCOUNT.balances.map(
                                      (balance, index) => (
                                        <tr key={index}>
                                          <td
                                            style={{
                                              padding: '8px 0',
                                              color: balance.color,
                                              fontWeight: '500',
                                            }}
                                          >
                                            {balance.currency}
                                          </td>
                                          <td style={{ padding: '8px 0' }}>
                                            {balance.amount}
                                          </td>
                                          <td style={{ padding: '8px 0' }}>
                                            {balance.type}
                                          </td>
                                        </tr>
                                      )
                                    )}
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </div>

                          {/* Exchange Rates Card */}
                          <h6 className="mb-2">
                            Live Exchange Rates Against Base Currency
                          </h6>
                          <div className="d-card account-balance-card">
                            <div className="d-flex justify-content-between align-items-center mb-3">
                              <div className="d-flex align-items-center account-name w-100">
                                <span
                                  className="me-2"
                                  style={{ color: '#6B7280' }}
                                >
                                  Inverse
                                </span>
                                <div className="form-check form-switch">
                                  <input
                                    className="form-check-input"
                                    type="checkbox"
                                    style={{ cursor: 'pointer' }}
                                  />
                                </div>
                              </div>
                            </div>
                            <table className="w-100">
                              <thead>
                                <tr
                                  style={{ borderBottom: '1px solid #E5E7EB' }}
                                >
                                  <th
                                    style={{
                                      padding: '8px 0',
                                      color: '#6B7280',
                                      fontWeight: '500',
                                    }}
                                  >
                                    FCy
                                  </th>
                                  <th
                                    style={{
                                      padding: '8px 0',
                                      color: '#6B7280',
                                      fontWeight: '500',
                                    }}
                                  >
                                    Rates
                                  </th>
                                  <th
                                    style={{
                                      padding: '8px 0',
                                      color: '#6B7280',
                                      fontWeight: '500',
                                    }}
                                  >
                                    Change (24h)
                                  </th>
                                </tr>
                              </thead>
                              <tbody>
                                {MOCK_EXCHANGE_RATES.map((rate, index) => (
                                  <tr key={index}>
                                    <td style={{ padding: '8px 0' }}>
                                      {rate.currency}
                                    </td>
                                    <td style={{ padding: '8px 0' }}>
                                      {rate.rate}
                                    </td>
                                    <td
                                      style={{
                                        padding: '8px 0',
                                        color: rate.isPositive
                                          ? '#22C55E'
                                          : '#EF4444',
                                      }}
                                    >
                                      {rate.change}
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Form Footer */}
                  {/* <div className="d-flex mt-4 flex-xxl-nowrap flex-wrap gap-3 justify-content-between align-items-center">
                      {!isDisabled && (
                        <div className="d-flex gap-2">
                          <CustomButton
                            type="submit"
                            text="Save"
                          />
                          <CustomButton
                            text="Cancel"
                            variant="secondary"
                            onClick={() => {
                              formikProps.resetForm();
                              setIsDisabled(true);
                              setShowRightCards(false);
                            }}
                          />
                        </div>
                      )}
                      <div className="d-flex gap-3 align-items-center">
                        <div className="d-flex gap-2">
                          <FaChevronLeft size={20} style={{ cursor: isDisabled ? 'not-allowed' : 'pointer', opacity: isDisabled ? 0.5 : 1 }} />
                          <FaChevronRight size={20} style={{ cursor: isDisabled ? 'not-allowed' : 'pointer', opacity: isDisabled ? 0.5 : 1 }} />
                          <FaPaperclip
                            onClick={() => !isDisabled && setUploadAttachmentsModal(true)}
                            size={20}
                            style={{ cursor: isDisabled ? 'not-allowed' : 'pointer', opacity: isDisabled ? 0.5 : 1 }}
                          />
                        </div>
                      </div>
                    </div> */}
                  <VoucherNavigationBar
                    isDisabled={isDisabled}
                    actionButtons={[
                      { text: 'Save', onClick: handleSubmit },
                      {
                        text: 'Cancel',
                        onClick: () => {
                          formikProps.resetForm();
                          setIsDisabled(true);
                          setShowRightCards(false);
                        },
                        variant: 'secondaryButton',
                      },
                    ]}
                    onAttachmentClick={() => setUploadAttachmentsModal(true)}
                    loading={
                      searchTerm > 0
                        ? updateBankTransactionMutation.isPending
                        : createBankTransactionMutation.isPending
                    }
                    lastVoucherHeading={getLastNumberText(
                      headerTransactionType
                    )}
                    setPageState={setPageState}
                    lastVoucherNumbers={lastVoucherNumbers}
                    setSearchTerm={setSearchTerm}
                  />

                  {/* Checkboxes */}
                  <div className="mt-3">
                    <div></div>
                    <div className="d-inline-block mt-3">
                      <CustomCheckbox
                        style={{ border: 'none' }}
                        label="Account Balance"
                        disabled={isDisabled}
                      />
                      <CustomCheckbox
                        style={{ border: 'none' }}
                        label="Print"
                        disabled={isDisabled}
                      />
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          )}
        </div>
        {/*<VoucherNavigationBar*/}
        {/*  isDisabled={isDisabled}*/}
        {/*  actionButtons={[*/}
        {/*    { text: 'Save', onClick: handleSubmit },*/}
        {/*    {*/}
        {/*      text: 'Cancel',*/}
        {/*      onClick: () => {*/}
        {/*        formikProps.resetForm();*/}
        {/*        setIsDisabled(true);*/}
        {/*        setShowRightCards(false);*/}
        {/*      },*/}
        {/*      variant: 'secondaryButton',*/}
        {/*    },*/}
        {/*  ]}*/}
        {/*  onAttachmentClick={() => setUploadAttachmentsModal(true)}*/}
        {/*  loading={createBankTransactionMutation.isPending}*/}
        {/*  lastVoucherHeading={getLastNumberText(*/}
        {/*    headerTransactionType,*/}
        {/*  )}*/}
        {/*  lastVoucherNumbers={lastVoucherNumbers}*/}
        {/*/>*/}
      </section>
      <CustomModal
        show={!!showAddLedgerModal}
        close={() => setShowAddLedgerModal('')}
        size="xl"
        style={{ minHeight: '812px' }}
      >
        {renderAddLedgerForm()}
      </CustomModal>
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          uploadOnly
          getUploadedFiles={setAddedAttachments}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />

        {/*<AttachmentsView*/}
        {/*  item={supportLogsData[0]}*/}
        {/*  closeUploader={() => setUploadAttachmentsModal(false)}*/}
        {/*/>*/}
      </CustomModal>
      <CustomModal
        show={showMissingCurrencyRateModal}
        close={() => setShowMissingCurrencyRateModal(false)}
        title={'Missing Rate of Exchange'}
        description={'Rate of exchange is missing for selected currency.'}
        variant={'error'}
        btn1Text={'Update Rate of Exchange'}
        action={() => {
          navigate('/transactions/remittance-rate-of-exchange');
        }}
      />
    </>
  );
};

export default withFilters(withModal(BankTransactions));
